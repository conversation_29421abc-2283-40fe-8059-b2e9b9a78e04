% Mathematical Notation Guide
% Comprehensive guide to all mathematical symbols used in the document
% To be integrated into the main document

\section{Comprehensive Mathematical Notation Guide}

This section provides detailed explanations of all mathematical symbols, notation conventions, and terminology used throughout this document. Each symbol is defined when first introduced and includes pharmaceutical context where applicable.

\subsection{Basic Mathematical Symbols}

\subsubsection{Arithmetic Operations}
\begin{itemize}
\item $+$ : Addition operator
  \begin{itemize}
  \item \textbf{Example}: $C_1 + C_2$ represents the sum of concentrations in compartments 1 and 2
  \item \textbf{Pharmaceutical context}: Combined drug concentrations, additive effects
  \end{itemize}

\item $-$ : Subtraction operator
  \begin{itemize}
  \item \textbf{Example}: $C_{final} - C_{initial}$ represents change in concentration
  \item \textbf{Pharmaceutical context}: Concentration differences, net drug elimination
  \end{itemize}

\item $\times$ or $\cdot$ : Multiplication operator
  \begin{itemize}
  \item \textbf{Example}: $k \times A$ or $k \cdot A$ represents rate constant times amount
  \item \textbf{Pharmaceutical context}: Rate calculations, dose adjustments
  \end{itemize}

\item $\div$ or $/$ : Division operator
  \begin{itemize}
  \item \textbf{Example}: $\text{Dose}/\text{Weight}$ represents dose per unit body weight
  \item \textbf{Pharmaceutical context}: Dose normalization, concentration calculations
  \end{itemize}

\item $=$ : Equality operator
  \begin{itemize}
  \item \textbf{Example}: $C(t) = C_0 e^{-kt}$ defines concentration as a function of time
  \item \textbf{Pharmaceutical context}: Mathematical relationships, equilibrium conditions
  \end{itemize}

\item $\approx$ : Approximately equal to
  \begin{itemize}
  \item \textbf{Example}: $t_{1/2} \approx 0.693/k$ for first-order elimination
  \item \textbf{Pharmaceutical context}: Clinical approximations, rule-of-thumb calculations
  \end{itemize}

\item $\propto$ : Proportional to
  \begin{itemize}
  \item \textbf{Example}: $\text{Effect} \propto \text{Concentration}$ for linear dose-response
  \item \textbf{Pharmaceutical context}: Direct relationships between variables
  \end{itemize}
\end{itemize}

\subsubsection{Comparison Operators}
\begin{itemize}
\item $<$ : Less than
  \begin{itemize}
  \item \textbf{Example}: $C < MIC$ means concentration is below minimum inhibitory concentration
  \item \textbf{Pharmaceutical context}: Therapeutic thresholds, safety limits
  \end{itemize}

\item $>$ : Greater than
  \begin{itemize}
  \item \textbf{Example}: $\text{Dose} > \text{ED}_{50}$ means dose exceeds median effective dose
  \item \textbf{Pharmaceutical context}: Efficacy thresholds, toxicity limits
  \end{itemize}

\item $\leq$ : Less than or equal to
  \begin{itemize}
  \item \textbf{Example}: $C_{max} \leq \text{Safety Limit}$ ensures concentration stays within safe range
  \item \textbf{Pharmaceutical context}: Safety constraints, regulatory limits
  \end{itemize}

\item $\geq$ : Greater than or equal to
  \begin{itemize}
  \item \textbf{Example}: $\text{AUC} \geq \text{Target AUC}$ ensures adequate drug exposure
  \item \textbf{Pharmaceutical context}: Efficacy requirements, bioequivalence criteria
  \end{itemize}

\item $\neq$ : Not equal to
  \begin{itemize}
  \item \textbf{Example}: $k_{12} \neq k_{21}$ indicates different transfer rates between compartments
  \item \textbf{Pharmaceutical context}: Asymmetric processes, non-equilibrium conditions
  \end{itemize}
\end{itemize}

\subsection{Variables and Parameters}

\subsubsection{Concentration and Amount Variables}
\begin{itemize}
\item $C$ : Concentration (typically mg/L or μg/mL)
  \begin{itemize}
  \item \textbf{Subscripts}: $C_0$ (initial), $C_{max}$ (maximum), $C_{min}$ (minimum)
  \item \textbf{Function notation}: $C(t)$ indicates concentration as a function of time
  \item \textbf{Pharmaceutical context}: Plasma drug concentration, tissue concentration
  \end{itemize}

\item $A$ : Amount of drug (typically mg or μg)
  \begin{itemize}
  \item \textbf{Subscripts}: $A_1, A_2$ for different compartments
  \item \textbf{Function notation}: $A(t)$ indicates amount as a function of time
  \item \textbf{Pharmaceutical context}: Total drug amount in body or compartment
  \end{itemize}

\item $D$ : Dose (typically mg)
  \begin{itemize}
  \item \textbf{Variations}: $D_0$ (initial dose), $D_{daily}$ (daily dose)
  \item \textbf{Pharmaceutical context}: Administered drug amount
  \end{itemize}
\end{itemize}

\subsubsection{Rate Constants and Kinetic Parameters}
\begin{itemize}
\item $k$ : Rate constant (units vary by process)
  \begin{itemize}
  \item \textbf{First-order}: $k$ has units of time$^{-1}$ (e.g., hr$^{-1}$)
  \item \textbf{Subscripts}: $k_{10}$ (elimination from compartment 1), $k_{12}$ (transfer from 1 to 2)
  \item \textbf{Pharmaceutical context}: Elimination rate, absorption rate, distribution rate
  \end{itemize}

\item $k_a$ : Absorption rate constant (hr$^{-1}$)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Rate of drug absorption from dosing site
  \end{itemize}

\item $k_e$ or $k_{el}$ : Elimination rate constant (hr$^{-1}$)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Overall rate of drug removal from body
  \end{itemize}

\item $\alpha, \beta$ : Distribution and elimination phase rate constants (hr$^{-1}$)
  \begin{itemize}
  \item \textbf{Convention}: $\alpha > \beta$ (distribution is faster than elimination)
  \item \textbf{Pharmaceutical context}: Two-compartment model parameters
  \end{itemize}
\end{itemize}

\subsubsection{Volume and Clearance Parameters}
\begin{itemize}
\item $V$ : Volume of distribution (L or L/kg)
  \begin{itemize}
  \item \textbf{Subscripts}: $V_1$ (central), $V_2$ (peripheral), $V_{ss}$ (steady-state)
  \item \textbf{Pharmaceutical context}: Apparent volume drug distributes into
  \end{itemize}

\item $CL$ : Clearance (L/hr or mL/min)
  \begin{itemize}
  \item \textbf{Types}: $CL_{renal}$ (kidney), $CL_{hepatic}$ (liver), $CL_{total}$ (overall)
  \item \textbf{Pharmaceutical context}: Volume of plasma cleared of drug per unit time
  \end{itemize}

\item $Q$ : Blood flow rate (L/hr)
  \begin{itemize}
  \item \textbf{Subscripts}: $Q_H$ (hepatic), $Q_R$ (renal)
  \item \textbf{Pharmaceutical context}: Organ blood flow in PBPK models
  \end{itemize}
\end{itemize}

\subsection{Pharmacodynamic Notation}

\subsubsection{Effect and Response Variables}
\begin{itemize}
\item $E$ : Effect or response (units vary)
  \begin{itemize}
  \item \textbf{Subscripts}: $E_0$ (baseline), $E_{max}$ (maximum), $E_{min}$ (minimum)
  \item \textbf{Pharmaceutical context}: Measured drug effect (blood pressure, heart rate, etc.)
  \end{itemize}

\item $EC_{50}$ : Concentration producing 50\% of maximum effect
  \begin{itemize}
  \item \textbf{Units}: Same as concentration (mg/L, μg/mL)
  \item \textbf{Pharmaceutical context}: Measure of drug potency
  \end{itemize}

\item $ED_{50}$ : Dose producing effect in 50\% of subjects
  \begin{itemize}
  \item \textbf{Units}: Same as dose (mg, mg/kg)
  \item \textbf{Pharmaceutical context}: Median effective dose
  \end{itemize}

\item $TD_{50}$ : Dose producing toxicity in 50\% of subjects
  \begin{itemize}
  \item \textbf{Units}: Same as dose (mg, mg/kg)
  \item \textbf{Pharmaceutical context}: Median toxic dose
  \end{itemize}
\end{itemize}

\subsubsection{Binding and Interaction Parameters}
\begin{itemize}
\item $K_d$ : Dissociation constant (mg/L or μM)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Measure of drug-receptor binding affinity
  \item \textbf{Interpretation}: Lower $K_d$ means higher affinity
  \end{itemize}

\item $K_m$ : Michaelis constant (mg/L or μM)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Substrate concentration at half-maximal enzyme velocity
  \item \textbf{Interpretation}: Measure of enzyme-substrate affinity
  \end{itemize}

\item $V_{max}$ : Maximum velocity (mg/hr or μmol/min)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Maximum rate of enzyme-catalyzed reaction
  \end{itemize}

\item $K_i$ : Inhibition constant (mg/L or μM)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Measure of inhibitor potency
  \end{itemize}
\end{itemize}

\subsection{Mathematical Functions and Operations}

\subsubsection{Exponential and Logarithmic Functions}
\begin{itemize}
\item $e$ : Euler's number (≈ 2.718)
  \begin{itemize}
  \item \textbf{Usage}: Base of natural exponential function
  \item \textbf{Pharmaceutical context}: Appears in first-order kinetic equations
  \end{itemize}

\item $e^x$ : Exponential function
  \begin{itemize}
  \item \textbf{Properties}: Always positive, monotonically increasing
  \item \textbf{Pharmaceutical context}: Describes exponential growth or decay
  \end{itemize}

\item $\ln(x)$ : Natural logarithm (base $e$)
  \begin{itemize}
  \item \textbf{Properties}: Inverse of exponential function
  \item \textbf{Pharmaceutical context}: Linearizes exponential relationships
  \end{itemize}

\item $\log_{10}(x)$ : Common logarithm (base 10)
  \begin{itemize}
  \item \textbf{Usage}: Often used in pH calculations, log-dose plots
  \item \textbf{Pharmaceutical context}: Dose-response curves, concentration scales
  \end{itemize}
\end{itemize}

\subsubsection{Trigonometric and Hyperbolic Functions}
\begin{itemize}
\item $\sin(x), \cos(x), \tan(x)$ : Trigonometric functions
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Periodic dosing, circadian rhythms
  \end{itemize}

\item $\sinh(x), \cosh(x), \tanh(x)$ : Hyperbolic functions
  \begin{itemize}
  \item \textbf{Note}: $\tanh(x)$ is used as activation function in neural networks
  \item \textbf{Pharmaceutical context}: Sigmoid-like dose-response relationships
  \end{itemize}
\end{itemize}

\subsection{Calculus Notation}

\subsubsection{Derivatives}
\begin{itemize}
\item $\frac{d}{dx}$ : Derivative operator with respect to $x$
  \begin{itemize}
  \item \textbf{Example}: $\frac{dC}{dt}$ represents rate of change of concentration with time
  \item \textbf{Pharmaceutical context}: Rates of drug processes
  \end{itemize}

\item $\frac{df}{dx}$ : Derivative of function $f$ with respect to $x$
  \begin{itemize}
  \item \textbf{Interpretation}: Instantaneous rate of change
  \item \textbf{Pharmaceutical context}: Slope of concentration-time curves
  \end{itemize}

\item $f'(x)$ : Alternative notation for derivative
  \begin{itemize}
  \item \textbf{Usage}: Shorthand for $\frac{df}{dx}$
  \end{itemize}

\item $\frac{\partial f}{\partial x}$ : Partial derivative
  \begin{itemize}
  \item \textbf{Usage}: Derivative with respect to $x$ while holding other variables constant
  \item \textbf{Pharmaceutical context}: Sensitivity analysis, parameter estimation
  \end{itemize}
\end{itemize}

\subsubsection{Integrals}
\begin{itemize}
\item $\int f(x) dx$ : Indefinite integral
  \begin{itemize}
  \item \textbf{Interpretation}: Antiderivative of $f(x)$
  \item \textbf{Pharmaceutical context}: Solving differential equations
  \end{itemize}

\item $\int_a^b f(x) dx$ : Definite integral from $a$ to $b$
  \begin{itemize}
  \item \textbf{Interpretation}: Area under curve between $a$ and $b$
  \item \textbf{Pharmaceutical context}: AUC (Area Under Curve) calculations
  \end{itemize}

\item $\text{AUC}$ : Area Under the Curve
  \begin{itemize}
  \item \textbf{Formula}: $\text{AUC} = \int_0^{\infty} C(t) dt$
  \item \textbf{Units}: Concentration × time (e.g., mg⋅hr/L)
  \item \textbf{Pharmaceutical context}: Measure of total drug exposure
  \end{itemize}
\end{itemize}

\subsection{Linear Algebra Notation}

\subsubsection{Vectors}
\begin{itemize}
\item $\mathbf{x}$ or $\vec{x}$ : Vector (bold or arrow notation)
  \begin{itemize}
  \item \textbf{Components}: $\mathbf{x} = \begin{pmatrix} x_1 \\ x_2 \\ \vdots \\ x_n \end{pmatrix}$
  \item \textbf{Pharmaceutical context}: Patient characteristics, drug properties
  \end{itemize}

\item $\mathbf{x}^T$ : Transpose of vector $\mathbf{x}$
  \begin{itemize}
  \item \textbf{Effect}: Converts column vector to row vector
  \item \textbf{Usage}: Matrix multiplication, dot products
  \end{itemize}

\item $\|\mathbf{x}\|$ : Norm (length) of vector $\mathbf{x}$
  \begin{itemize}
  \item \textbf{Euclidean norm}: $\|\mathbf{x}\|_2 = \sqrt{x_1^2 + x_2^2 + \cdots + x_n^2}$
  \item \textbf{Pharmaceutical context}: Distance between patient profiles
  \end{itemize}

\item $\mathbf{x} \cdot \mathbf{y}$ : Dot product of vectors
  \begin{itemize}
  \item \textbf{Formula}: $\mathbf{x} \cdot \mathbf{y} = \sum_{i=1}^n x_i y_i$
  \item \textbf{Pharmaceutical context}: Similarity measures, weighted scores
  \end{itemize}
\end{itemize}

\subsubsection{Matrices}
\begin{itemize}
\item $\mathbf{A}$ or $A$ : Matrix (bold or regular notation)
  \begin{itemize}
  \item \textbf{Elements}: $A_{ij}$ represents element in row $i$, column $j$
  \item \textbf{Pharmaceutical context}: Data matrices, transformation matrices
  \end{itemize}

\item $\mathbf{A}^T$ : Transpose of matrix $\mathbf{A}$
  \begin{itemize}
  \item \textbf{Effect}: $(A^T)_{ij} = A_{ji}$ (rows become columns)
  \end{itemize}

\item $\mathbf{A}^{-1}$ : Inverse of matrix $\mathbf{A}$
  \begin{itemize}
  \item \textbf{Property}: $\mathbf{A}\mathbf{A}^{-1} = \mathbf{I}$ (identity matrix)
  \item \textbf{Pharmaceutical context}: Solving systems of equations
  \end{itemize}

\item $\det(\mathbf{A})$ : Determinant of matrix $\mathbf{A}$
  \begin{itemize}
  \item \textbf{Usage}: Indicates if matrix is invertible (non-zero determinant)
  \end{itemize}

\item $\text{tr}(\mathbf{A})$ : Trace of matrix $\mathbf{A}$
  \begin{itemize}
  \item \textbf{Formula}: Sum of diagonal elements
  \end{itemize}
\end{itemize}

\subsection{Statistical Notation}

\subsubsection{Probability and Distributions}
\begin{itemize}
\item $P(A)$ : Probability of event $A$
  \begin{itemize}
  \item \textbf{Range}: $0 \leq P(A) \leq 1$
  \item \textbf{Pharmaceutical context}: Probability of adverse events, treatment success
  \end{itemize}

\item $P(A|B)$ : Conditional probability of $A$ given $B$
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Probability of response given patient characteristics
  \end{itemize}

\item $\mu$ : Mean (population parameter)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Average drug concentration, mean response
  \end{itemize}

\item $\sigma$ : Standard deviation (population parameter)
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Variability in drug response
  \end{itemize}

\item $\sigma^2$ : Variance (population parameter)
  \begin{itemize}
  \item \textbf{Relationship}: $\sigma^2 = \text{Var}(X)$
  \end{itemize}

\item $\bar{x}$ : Sample mean
  \begin{itemize}
  \item \textbf{Formula}: $\bar{x} = \frac{1}{n}\sum_{i=1}^n x_i$
  \end{itemize}

\item $s$ : Sample standard deviation
  \begin{itemize}
  \item \textbf{Formula}: $s = \sqrt{\frac{1}{n-1}\sum_{i=1}^n (x_i - \bar{x})^2}$
  \end{itemize}
\end{itemize}

\subsubsection{Distributions}
\begin{itemize}
\item $N(\mu, \sigma^2)$ : Normal distribution with mean $\mu$ and variance $\sigma^2$
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Distribution of drug concentrations, measurement errors
  \end{itemize}

\item $\Phi(x)$ : Cumulative distribution function of standard normal distribution
  \begin{itemize}
  \item \textbf{Usage}: Probit analysis, probability calculations
  \end{itemize}

\item $\chi^2$ : Chi-squared distribution
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Goodness-of-fit tests, model comparison
  \end{itemize}
\end{itemize}

\subsection{Neural Network Notation}

\subsubsection{Network Architecture}
\begin{itemize}
\item $L$ : Number of layers in neural network
  \begin{itemize}
  \item \textbf{Convention}: Input layer is layer 0, output layer is layer $L$
  \end{itemize}

\item $n^{(\ell)}$ : Number of neurons in layer $\ell$
  \begin{itemize}
  \item \textbf{Example}: $n^{(0)}$ is input dimension, $n^{(L)}$ is output dimension
  \end{itemize}

\item $\mathbf{W}^{(\ell)}$ : Weight matrix for layer $\ell$
  \begin{itemize}
  \item \textbf{Dimensions}: $n^{(\ell)} \times n^{(\ell-1)}$
  \item \textbf{Elements}: $W_{ij}^{(\ell)}$ is weight from neuron $j$ in layer $\ell-1$ to neuron $i$ in layer $\ell$
  \end{itemize}

\item $\mathbf{b}^{(\ell)}$ : Bias vector for layer $\ell$
  \begin{itemize}
  \item \textbf{Dimensions}: $n^{(\ell)} \times 1$
  \end{itemize}
\end{itemize}

\subsubsection{Activation Functions}
\begin{itemize}
\item $\sigma(x)$ : Sigmoid activation function
  \begin{itemize}
  \item \textbf{Formula}: $\sigma(x) = \frac{1}{1 + e^{-x}}$
  \item \textbf{Range}: $(0, 1)$
  \item \textbf{Pharmaceutical context}: Probability of response, dose-response curves
  \end{itemize}

\item $\tanh(x)$ : Hyperbolic tangent activation function
  \begin{itemize}
  \item \textbf{Formula}: $\tanh(x) = \frac{e^x - e^{-x}}{e^x + e^{-x}}$
  \item \textbf{Range}: $(-1, 1)$
  \end{itemize}

\item $\text{ReLU}(x)$ : Rectified Linear Unit
  \begin{itemize}
  \item \textbf{Formula}: $\text{ReLU}(x) = \max(0, x)$
  \item \textbf{Pharmaceutical context}: Threshold effects, clearance mechanisms
  \end{itemize}

\item $\text{softmax}(\mathbf{x})$ : Softmax function
  \begin{itemize}
  \item \textbf{Formula}: $\text{softmax}(\mathbf{x})_i = \frac{e^{x_i}}{\sum_{j=1}^n e^{x_j}}$
  \item \textbf{Pharmaceutical context}: Treatment selection probabilities
  \end{itemize}
\end{itemize}

\subsection{Special Symbols and Operators}

\subsubsection{Set Theory}
\begin{itemize}
\item $\mathbb{R}$ : Set of real numbers
  \begin{itemize}
  \item \textbf{Usage}: $\mathbf{x} \in \mathbb{R}^n$ means $\mathbf{x}$ is a vector of $n$ real numbers
  \end{itemize}

\item $\mathbb{R}^{n \times m}$ : Set of $n \times m$ real matrices
  \begin{itemize}
  \item \textbf{Usage}: Specifies matrix dimensions
  \end{itemize}

\item $\in$ : Element of (belongs to)
  \begin{itemize}
  \item \textbf{Example}: $x \in [0, 1]$ means $x$ is between 0 and 1
  \end{itemize}

\item $\subset$ : Subset of
  \begin{itemize}
  \item \textbf{Example}: $A \subset B$ means all elements of $A$ are in $B$
  \end{itemize}
\end{itemize}

\subsubsection{Summation and Product}
\begin{itemize}
\item $\sum_{i=1}^n$ : Summation from $i=1$ to $n$
  \begin{itemize}
  \item \textbf{Example}: $\sum_{i=1}^n x_i = x_1 + x_2 + \cdots + x_n$
  \item \textbf{Pharmaceutical context}: Total dose, cumulative effects
  \end{itemize}

\item $\prod_{i=1}^n$ : Product from $i=1$ to $n$
  \begin{itemize}
  \item \textbf{Example}: $\prod_{i=1}^n x_i = x_1 \times x_2 \times \cdots \times x_n$
  \end{itemize}
\end{itemize}

\subsubsection{Limits and Infinity}
\begin{itemize}
\item $\lim_{x \to a}$ : Limit as $x$ approaches $a$
  \begin{itemize}
  \item \textbf{Pharmaceutical context}: Steady-state conditions, long-term behavior
  \end{itemize}

\item $\infty$ : Infinity
  \begin{itemize}
  \item \textbf{Usage}: $\int_0^{\infty}$ for total AUC calculation
  \end{itemize}

\item $\to$ : Approaches or tends to
  \begin{itemize}
  \item \textbf{Example}: $C(t) \to 0$ as $t \to \infty$ for drug elimination
  \end{itemize}
\end{itemize}

\subsection{Pharmaceutical-Specific Notation}

\subsubsection{Pharmacokinetic Parameters}
\begin{itemize}
\item $t_{1/2}$ : Half-life
  \begin{itemize}
  \item \textbf{Definition}: Time for concentration to decrease by 50\%
  \item \textbf{Formula}: $t_{1/2} = \frac{\ln(2)}{k}$ for first-order elimination
  \end{itemize}

\item $\text{AUC}_{0-\infty}$ : Area under curve from time 0 to infinity
  \begin{itemize}
  \item \textbf{Units}: Concentration × time
  \item \textbf{Clinical significance}: Total drug exposure
  \end{itemize}

\item $C_{max}$ : Maximum concentration
  \begin{itemize}
  \item \textbf{Context}: Peak concentration after dose administration
  \end{itemize}

\item $T_{max}$ : Time to maximum concentration
  \begin{itemize}
  \item \textbf{Context}: Time when peak concentration occurs
  \end{itemize}

\item $F$ : Bioavailability
  \begin{itemize}
  \item \textbf{Range}: $0 \leq F \leq 1$ (often expressed as percentage)
  \item \textbf{Definition}: Fraction of dose reaching systemic circulation
  \end{itemize}
\end{itemize}

\subsubsection{Pharmacodynamic Parameters}
\begin{itemize}
\item $\text{IC}_{50}$ : Concentration causing 50\% inhibition
  \begin{itemize}
  \item \textbf{Context}: Measure of inhibitor potency
  \end{itemize}

\item $\text{MIC}$ : Minimum inhibitory concentration
  \begin{itemize}
  \item \textbf{Context}: Lowest concentration preventing bacterial growth
  \end{itemize}

\item $\text{NOAEL}$ : No Observed Adverse Effect Level
  \begin{itemize}
  \item \textbf{Context}: Highest dose with no adverse effects
  \end{itemize}

\item $\text{LOAEL}$ : Lowest Observed Adverse Effect Level
  \begin{itemize}
  \item \textbf{Context}: Lowest dose causing adverse effects
  \end{itemize}
\end{itemize}

\subsection{Cross-References and Usage Guidelines}

\subsubsection{When Symbols Are First Introduced}
Each mathematical symbol should be:
\begin{enumerate}
\item Clearly defined when first used
\item Given appropriate units
\item Explained in pharmaceutical context
\item Cross-referenced to this notation guide
\end{enumerate}

\subsubsection{Consistency Rules}
\begin{itemize}
\item Use bold notation for vectors and matrices: $\mathbf{x}, \mathbf{W}$
\item Use italics for scalar variables: $C, k, t$
\item Use subscripts for specification: $C_{max}, k_{12}, V_1$
\item Use superscripts for powers and layer indices: $x^2, W^{(\ell)}$
\item Use parentheses for function arguments: $C(t), f(x)$
\end{itemize}

\subsubsection{Common Pharmaceutical Contexts}
\begin{itemize}
\item \textbf{Concentration-time profiles}: $C(t) = C_0 e^{-kt}$
\item \textbf{Dose-response relationships}: $E = E_0 + \frac{E_{max} \cdot C}{EC_{50} + C}$
\item \textbf{Compartmental models}: $\frac{dA_1}{dt} = k_{21}A_2 - (k_{12} + k_{10})A_1$
\item \textbf{Neural network transformations}: $\mathbf{y} = \sigma(\mathbf{W}\mathbf{x} + \mathbf{b})$
\end{itemize}

This comprehensive notation guide ensures that all mathematical symbols are clearly defined and understood in their pharmaceutical context, supporting the learning objectives for clinical pharmacologists studying neural network mathematics.