% New Matrix Visualizations
% Contains only the NEW matrix-related figures needed for integration

% Figure: SVD Visualization
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % Original matrix A
    \draw[thick] (0,0) rectangle (2,3);
    \node at (1,1.5) {$A$};
    \node[below] at (1,-0.3) {$m \times n$};
    
    % Equals sign
    \node at (2.5,1.5) {$=$};
    
    % U matrix
    \draw[thick,blue] (3,0) rectangle (4.5,3);
    \node[blue] at (3.75,1.5) {$U$};
    \node[blue,below] at (3.75,-0.3) {$m \times m$};
    
    % Sigma matrix
    \draw[thick,red] (5,0.5) rectangle (7,2.5);
    \node[red] at (6,1.5) {$\Sigma$};
    \node[red,below] at (6,-0.3) {$m \times n$};
    
    % Diagonal elements in Sigma
    \foreach \i in {0,1} {
        \fill[red] (5.3+\i*0.7,0.8+\i*0.7) rectangle (5.7+\i*0.7,1.2+\i*0.7);
    }
    
    % V^T matrix
    \draw[thick,green] (7.5,0.5) rectangle (9.5,2.5);
    \node[green] at (8.5,1.5) {$V^T$};
    \node[green,below] at (8.5,-0.3) {$n \times n$};
    
    % Interpretation below
    \node[below] at (4.75,-1) {Singular Value Decomposition: $A = U\Sigma V^T$};
    \node[below] at (4.75,-1.5) {Orthogonal matrices $U$, $V$ and diagonal $\Sigma$ with singular values};
    
    % Clinical interpretation
    \node[below] at (4.75,-2.5) {\textbf{Pharmaceutical Application:}};
    \node[below] at (4.75,-3) {Patient-Drug response matrix $A$ decomposes into};
    \node[below] at (4.75,-3.5) {patient patterns ($U$), response strengths ($\Sigma$), and drug patterns ($V$)};
    
\end{tikzpicture}
\caption{SVD Visualization: Singular Value Decomposition factorizes a matrix into orthogonal components. In pharmaceutical applications, SVD can decompose patient-drug response matrices to identify latent patterns in how different patient types respond to various medications, enabling personalized treatment recommendations.}
\label{fig:svd_visualization}
\end{figure}

% Figure: Linear System Visualization
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.2]
    % Coordinate system
    \draw[->] (-0.5,0) -- (4,0) node[right] {Drug A Dose (mg)};
    \draw[->] (0,-0.5) -- (0,4) node[above] {Drug B Dose (mg)};
    
    % Constraint lines
    % Efficacy constraint: x + y >= 2
    \draw[blue, thick] (0,2) -- (2,0);
    \fill[blue, opacity=0.2] (2,0) -- (4,0) -- (4,2) -- (2,0);
    \node[blue] at (0.5,2.5) {Efficacy};
    \node[blue] at (0.5,2.2) {$x + y \geq 2$};
    
    % Safety constraint: 2x + y <= 5
    \draw[red, thick] (0,5) -- (2.5,0);
    \fill[red, opacity=0.2] (0,0) -- (2.5,0) -- (0,5) -- (0,0);
    \node[red] at (3,3.5) {Safety};
    \node[red] at (3,3.2) {$2x + y \leq 5$};
    
    % Feasible region
    \fill[green, opacity=0.3] (0,2) -- (1,3) -- (2,1) -- (2,0) -- (0,2);
    \node[green] at (1.2,1.8) {Feasible};
    \node[green] at (1.2,1.5) {Region};
    
    % Optimal point
    \fill[black] (1,3) circle (0.08);
    \node[above] at (1,3.2) {Optimal};
    \node[above] at (1,2.9) {$(1,3)$};
    
    % Grid
    \foreach \x in {0,1,2,3,4} {
        \draw[gray, thin] (\x,0) -- (\x,4);
    }
    \foreach \y in {0,1,2,3,4} {
        \draw[gray, thin] (0,\y) -- (4,\y);
    }
    
\end{tikzpicture}
\caption{Linear System Visualization: Drug dosing optimization as a linear programming problem. The feasible region (green) satisfies both efficacy and safety constraints. The optimal dosing combination minimizes side effects while maintaining therapeutic benefit, demonstrating how linear algebra solves real-world pharmaceutical optimization problems.}
\label{fig:linear_system}
\end{figure}

% Figure: Transformation Composition
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % Original vector
    \draw[->] (0,0) -- (1,0.5) node[above right] {$\vec{v}$};
    \draw[blue] (0,0) rectangle (1,0.5);
    \node[below] at (0.5,-0.3) {Original};
    
    % Arrow 1
    \draw[->,thick] (1.5,0.25) -- (2.5,0.25);
    \node[above] at (2,0.5) {$T_1$};
    
    % After first transformation
    \draw[->] (3,0) -- (3.5,1) node[above right] {$T_1\vec{v}$};
    \draw[red] (3,0) -- (3.5,1) -- (3.2,1.2) -- (2.7,0.2) -- (3,0);
    \node[below] at (3.25,-0.3) {After $T_1$};
    
    % Arrow 2
    \draw[->,thick] (4,0.5) -- (5,0.5);
    \node[above] at (4.5,0.75) {$T_2$};
    
    % After second transformation
    \draw[->] (5.5,0) -- (6.8,0.6) node[above right] {$T_2T_1\vec{v}$};
    \draw[green] (5.5,0) -- (6.8,0.6) -- (6.5,1.4) -- (5.2,0.8) -- (5.5,0);
    \node[below] at (6.15,-0.3) {After $T_2 \circ T_1$};
    
    % Composition explanation
    \node[below] at (3.5,-1.5) {Transformation Composition: $(T_2 \circ T_1)\vec{v} = T_2(T_1\vec{v})$};
    
    % Neural network analogy
    \node[below] at (3.5,-2.5) {\textbf{Neural Network Analogy:}};
    \node[below] at (3.5,-3) {Each layer applies a transformation: $\vec{h}^{(l+1)} = \sigma(W^{(l)}\vec{h}^{(l)} + \vec{b}^{(l)})$};
    \node[below] at (3.5,-3.5) {Deep networks compose many transformations to learn complex mappings};
    
\end{tikzpicture}
\caption{Transformation Composition: Sequential application of linear transformations demonstrates how complex mappings emerge from simple operations. In neural networks, each layer applies a transformation to the previous layer's output, and the composition of many layers enables learning of highly complex patterns in pharmaceutical data.}
\label{fig:transformation_composition}
\end{figure}