% Geometric Interpretations of Mathematical Concepts
% This file contains TikZ diagrams for visualizing mathematical concepts

\usepackage{tikz}
\usepackage{pgfplots}
\pgfplotsset{compat=1.18}
\usetikzlibrary{arrows.meta,positioning,shapes.geometric,calc,decorations.pathreplacing}

% Vector Operations and Geometric Interpretations

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.2]
    % Coordinate system
    \draw[->] (-0.5,0) -- (4,0) node[right] {$x$};
    \draw[->] (0,-0.5) -- (0,3.5) node[above] {$y$};
    
    % Vector A (patient age and weight representation)
    \draw[->,thick,blue] (0,0) -- (3,1) node[midway,above left] {$\vec{A}$};
    \draw[dashed,blue] (3,0) -- (3,1);
    \draw[dashed,blue] (0,1) -- (3,1);
    
    % Vector B (drug dosage and frequency)
    \draw[->,thick,red] (0,0) -- (1,2.5) node[midway,left] {$\vec{B}$};
    \draw[dashed,red] (1,0) -- (1,2.5);
    \draw[dashed,red] (0,2.5) -- (1,2.5);
    
    % Vector sum A + B
    \draw[->,thick,green] (0,0) -- (4,3.5) node[midway,above right] {$\vec{A} + \vec{B}$};
    
    % Parallelogram construction
    \draw[dotted,gray] (3,1) -- (4,3.5);
    \draw[dotted,gray] (1,2.5) -- (4,3.5);
    
    % Labels for components
    \node[below] at (1.5,0) {Age: 65 years};
    \node[left] at (0,0.5) {Weight: 70 kg};
    \node[below] at (0.5,0) {Dose: 10 mg};
    \node[left] at (0,1.25) {Frequency: 2/day};
    
    % Angle indication
    \draw[arc] (0.5,0) arc (0:18.4:0.5);
    \node at (0.7,0.2) {$\theta$};
\end{tikzpicture}
\caption{Vector Addition in Pharmaceutical Context: Patient characteristics (vector A) and treatment parameters (vector B) combine to determine therapeutic outcome (vector A + B). The parallelogram rule shows how multiple factors contribute to the overall treatment effect.}
\label{fig:vector_addition}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.5]
    % Two vectors for dot product
    \draw[->] (-0.5,0) -- (3.5,0) node[right] {$x$};
    \draw[->] (0,-0.5) -- (0,2.5) node[above] {$y$};
    
    % Vector u (drug concentration over time)
    \draw[->,thick,blue] (0,0) -- (3,1) node[above right] {$\vec{u}$};
    
    % Vector v (therapeutic effect over time)
    \draw[->,thick,red] (0,0) -- (1.5,2) node[above left] {$\vec{v}$};
    
    % Projection of v onto u
    \draw[dashed,purple] (1.5,2) -- (2.4,0.8);
    \draw[->,thick,purple] (0,0) -- (2.4,0.8) node[below right] {proj$_{\vec{u}}\vec{v}$};
    
    % Right angle indicator
    \draw[purple] (2.2,0.8) -- (2.2,1.0) -- (2.4,1.0);
    
    % Angle between vectors
    \draw[arc] (0.8,0) arc (0:53:0.8);
    \node at (1.1,0.3) {$\theta$};
    
    % Labels
    \node[blue,below] at (1.5,-0.3) {Drug Concentration Profile};
    \node[red,left] at (-0.3,1) {Therapeutic Effect};
    \node[purple,below] at (1.2,-0.6) {Correlated Component};
\end{tikzpicture}
\caption{Dot Product Geometric Interpretation: The dot product $\vec{u} \cdot \vec{v} = |\vec{u}||\vec{v}|\cos\theta$ measures how much two pharmaceutical variables (like drug concentration and therapeutic effect) are aligned. The projection shows the component of therapeutic effect that correlates with concentration.}
\label{fig:dot_product}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.2]
    % 3D coordinate system (isometric projection)
    \draw[->] (0,0) -- (4,0) node[right] {$x_1$ (Age)};
    \draw[->] (0,0) -- (-1.5,2.5) node[left] {$x_2$ (Weight)};
    \draw[->] (0,0) -- (0,3) node[above] {$x_3$ (Creatinine)};
    
    % Unit vectors
    \draw[->,thick,red] (0,0) -- (1,0) node[below right] {$\vec{e_1}$};
    \draw[->,thick,green] (0,0) -- (-0.375,0.625) node[left] {$\vec{e_2}$};
    \draw[->,thick,blue] (0,0) -- (0,1) node[right] {$\vec{e_3}$};
    
    % Patient vector
    \draw[->,very thick,purple] (0,0) -- (2.5,-0.9375,2) node[above right] {$\vec{p}$};
    
    % Projections
    \draw[dashed,gray] (2.5,0) -- (2.5,-0.9375,2);
    \draw[dashed,gray] (-0.9375,1.5625) -- (2.5,-0.9375,2);
    \draw[dashed,gray] (0,2) -- (2.5,-0.9375,2);
    
    % Component labels
    \node[below] at (1.25,0) {65 years};
    \node[left] at (-0.47,0.78) {80 kg};
    \node[right] at (0,1) {1.2 mg/dL};
    
    \node[purple,above] at (1.25,1.5) {Patient Vector};
    \node[below] at (2,-2) {$\vec{p} = 65\vec{e_1} + 80\vec{e_2} + 1.2\vec{e_3}$};
\end{tikzpicture}
\caption{Three-Dimensional Patient Representation: A patient's clinical characteristics (age, weight, creatinine clearance) form a vector in 3D space. Each axis represents a different clinical parameter, and the patient vector shows their position in this clinical parameter space.}
\label{fig:3d_patient_vector}
\end{figure}

% Matrix Operations Visual Representations

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.8]
    % Matrix A representation
    \node at (-2,2) {$A = \begin{bmatrix} 
        a_{11} & a_{12} & a_{13} \\
        a_{21} & a_{22} & a_{23}
    \end{bmatrix}$};
    
    % Matrix B representation  
    \node at (2,2) {$B = \begin{bmatrix}
        b_{11} & b_{12} \\
        b_{21} & b_{22} \\
        b_{31} & b_{32}
    \end{bmatrix}$};
    
    % Visual representation of multiplication
    \draw[thick,blue] (-3.5,1.5) rectangle (-0.5,0.5);
    \draw[thick,red] (0.5,1.5) rectangle (3.5,-0.5);
    
    % Highlight row and column
    \draw[very thick,green] (-3.5,1.2) rectangle (-0.5,0.8);
    \draw[very thick,green] (1.2,1.5) rectangle (1.8,-0.5);
    
    % Result matrix
    \node at (0,-2) {$AB = \begin{bmatrix}
        c_{11} & c_{12} \\
        c_{21} & c_{22}
    \end{bmatrix}$};
    
    % Arrow showing computation
    \draw[->,thick,green] (-2,0.3) -- (0,-1.3);
    \node[green,right] at (0.5,-1) {$c_{11} = a_{11}b_{11} + a_{12}b_{21} + a_{13}b_{31}$};
    
    % Clinical interpretation
    \node[below,text width=10cm,align=center] at (0,-3.5) {
        \textbf{Clinical Example:} Matrix A represents patient characteristics (rows = patients, columns = age, weight, creatinine). Matrix B represents dosing factors (rows = characteristics, columns = morning dose, evening dose). The product AB gives personalized dosing recommendations for each patient.
    };
\end{tikzpicture}
\caption{Matrix Multiplication Visualization: The geometric interpretation shows how patient characteristics (matrix A) are transformed by dosing algorithms (matrix B) to produce personalized treatment recommendations (matrix AB). Each element of the result represents a weighted combination of patient factors.}
\label{fig:matrix_multiplication}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % Original coordinate system
    \draw[->] (-0.5,0) -- (3,0) node[right] {$x$};
    \draw[->] (0,-0.5) -- (0,3) node[above] {$y$};
    
    % Unit square
    \draw[thick,blue] (0,0) rectangle (1,1);
    \fill[blue,opacity=0.3] (0,0) rectangle (1,1);
    
    % Transformed square (linear transformation)
    \draw[thick,red] (0,0) -- (2,0.5) -- (2.5,2) -- (0.5,1.5) -- cycle;
    \fill[red,opacity=0.3] (0,0) -- (2,0.5) -- (2.5,2) -- (0.5,1.5) -- cycle;
    
    % Transformation vectors
    \draw[->,very thick,green] (0,0) -- (2,0.5) node[below right] {$T(\vec{e_1})$};
    \draw[->,very thick,orange] (0,0) -- (0.5,1.5) node[above left] {$T(\vec{e_2})$};
    
    % Labels
    \node[blue,below left] at (0.5,0.5) {Original};
    \node[red,above] at (1.25,1.25) {Transformed};
    
    % Transformation matrix
    \node[below] at (1.5,-1) {$T = \begin{bmatrix} 2 & 0.5 \\ 0.5 & 1.5 \end{bmatrix}$};
    
    % Clinical interpretation
    \node[below,text width=8cm,align=center] at (1.5,-2) {
        \textbf{Pharmacological Interpretation:} Linear transformation represents how drug metabolism enzymes (transformation matrix) convert substrate concentrations (blue square) into metabolite concentrations (red parallelogram).
    };
\end{tikzpicture}
\caption{Linear Transformation Visualization: A linear transformation changes the shape and orientation of geometric objects while preserving lines and ratios. In pharmacology, this represents how enzymatic processes transform drug concentrations in a predictable, linear manner.}
\label{fig:linear_transformation}
\end{figure}

% Function Behavior and Properties

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=12cm,
        height=8cm,
        xlabel={Drug Concentration (mg/L)},
        ylabel={Therapeutic Effect (\%)},
        xmin=0, xmax=50,
        ymin=0, ymax=110,
        grid=major,
        legend pos=south east,
        title={Comparison of Linear vs. Non-Linear Dose-Response Relationships}
    ]
    
    % Linear relationship
    \addplot[blue,thick,domain=0:50] {2*x};
    \addlegendentry{Linear: $E = 2C$}
    
    % Michaelis-Menten (hyperbolic)
    \addplot[red,thick,domain=0:50,samples=100] {100*x/(10+x)};
    \addlegendentry{Michaelis-Menten: $E = \frac{100C}{10+C}$}
    
    % Sigmoid (Hill equation)
    \addplot[green,thick,domain=0:50,samples=100] {100*x^2/(15^2+x^2)};
    \addlegendentry{Sigmoid: $E = \frac{100C^2}{15^2+C^2}$}
    
    % EC50 markers
    \draw[dashed,red] (axis cs:10,0) -- (axis cs:10,50);
    \draw[dashed,red] (axis cs:0,50) -- (axis cs:10,50);
    \node[red] at (axis cs:12,25) {$EC_{50}$};
    
    \draw[dashed,green] (axis cs:15,0) -- (axis cs:15,50);
    \draw[dashed,green] (axis cs:0,50) -- (axis cs:15,50);
    \node[green] at (axis cs:17,25) {$EC_{50}$};
    
    \end{axis}
\end{tikzpicture}
\caption{Function Behavior Comparison: Linear relationships show constant proportionality between dose and effect. Michaelis-Menten kinetics show saturation at high concentrations. Sigmoid relationships show threshold effects and cooperativity. The $EC_{50}$ represents the concentration producing 50\% of maximum effect.}
\label{fig:function_comparison}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=12cm,
        height=8cm,
        xlabel={Time (hours)},
        ylabel={Plasma Concentration (mg/L)},
        xmin=0, xmax=24,
        ymin=0.1, ymax=100,
        ymode=log,
        grid=major,
        legend pos=north east,
        title={Pharmacokinetic Profiles: One vs. Two Compartment Models}
    ]
    
    % One-compartment model
    \addplot[blue,thick,domain=0:24,samples=100] {50*exp(-0.1155*x)};
    \addlegendentry{One-compartment: $C = 50e^{-0.1155t}$}
    
    % Two-compartment model
    \addplot[red,thick,domain=0:24,samples=100] {30*exp(-1.5*x) + 20*exp(-0.08*x)};
    \addlegendentry{Two-compartment: $C = 30e^{-1.5t} + 20e^{-0.08t}$}
    
    % Half-life markers
    \draw[dashed,blue] (axis cs:6,0.1) -- (axis cs:6,100);
    \node[blue,rotate=90] at (axis cs:6.5,10) {$t_{1/2} = 6$ hr};
    
    % Distribution and elimination phases
    \draw[dashed,red] (axis cs:0.5,0.1) -- (axis cs:0.5,100);
    \node[red,rotate=90] at (axis cs:1,30) {Distribution};
    \draw[dashed,red] (axis cs:4,0.1) -- (axis cs:4,100);
    \node[red,rotate=90] at (axis cs:4.5,5) {Elimination};
    
    \end{axis}
\end{tikzpicture}
\caption{Pharmacokinetic Model Comparison: One-compartment models show simple exponential decay (straight line on semi-log plot). Two-compartment models show bi-exponential decay with distinct distribution and elimination phases. The geometric differences reflect underlying physiological processes.}
\label{fig:pk_models}
\end{figure>

% Algorithm Flowcharts

\begin{figure}[H]
\centering
\begin{tikzpicture}[
    node distance=1.5cm,
    start/.style={rectangle, rounded corners, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=green!30},
    process/.style={rectangle, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=blue!30},
    decision/.style={diamond, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=yellow!30},
    end/.style={rectangle, rounded corners, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=red!30}
]

    \node (start) [start] {Start: Patient Data};
    \node (input) [process, below of=start] {Input: Age, Weight, Creatinine};
    \node (calculate) [process, below of=input] {Calculate Creatinine Clearance};
    \node (decision1) [decision, below of=calculate] {CrCl < 30 mL/min?};
    \node (reduce) [process, below left of=decision1, xshift=-2cm] {Reduce Dose by 50\%};
    \node (decision2) [decision, below right of=decision1, xshift=2cm] {CrCl < 60 mL/min?};
    \node (reduce2) [process, below of=decision2] {Reduce Dose by 25\%};
    \node (normal) [process, below of=reduce2] {Standard Dose};
    \node (output) [process, below of=reduce, xshift=2cm, yshift=-1cm] {Calculate Final Dose};
    \node (end) [end, below of=output] {Dosing Recommendation};

    \draw [->] (start) -- (input);
    \draw [->] (input) -- (calculate);
    \draw [->] (calculate) -- (decision1);
    \draw [->] (decision1) -- node[anchor=east] {Yes} (reduce);
    \draw [->] (decision1) -- node[anchor=west] {No} (decision2);
    \draw [->] (decision2) -- node[anchor=east] {Yes} (reduce2);
    \draw [->] (decision2) -- node[anchor=west] {No} (normal);
    \draw [->] (reduce) -- (output);
    \draw [->] (reduce2) -- (output);
    \draw [->] (normal) -- (output);
    \draw [->] (output) -- (end);

\end{tikzpicture}
\caption{Renal Dose Adjustment Algorithm: This flowchart shows the step-by-step process for adjusting drug doses based on kidney function. Decision diamonds represent conditional logic, while rectangles represent calculations or actions. This algorithmic thinking parallels the decision-making processes in neural networks.}
\label{fig:dose_adjustment_algorithm}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[
    node distance=1.2cm,
    neuron/.style={circle, minimum size=1cm, draw=black, fill=blue!20},
    input/.style={circle, minimum size=0.8cm, draw=black, fill=green!20},
    output/.style={circle, minimum size=1cm, draw=black, fill=red!20}
]

    % Input layer
    \node (i1) [input] at (0,3) {$x_1$};
    \node (i2) [input] at (0,2) {$x_2$};
    \node (i3) [input] at (0,1) {$x_3$};
    \node (i4) [input] at (0,0) {$x_4$};
    
    % Hidden layer
    \node (h1) [neuron] at (3,2.5) {$h_1$};
    \node (h2) [neuron] at (3,1.5) {$h_2$};
    \node (h3) [neuron] at (3,0.5) {$h_3$};
    
    % Output layer
    \node (o1) [output] at (6,1.5) {$y$};
    
    % Connections with weights
    \draw[->] (i1) -- (h1) node[midway,above] {$w_{11}$};
    \draw[->] (i1) -- (h2) node[midway,above] {$w_{12}$};
    \draw[->] (i1) -- (h3) node[midway,above] {$w_{13}$};
    
    \draw[->] (i2) -- (h1) node[midway,above] {$w_{21}$};
    \draw[->] (i2) -- (h2) node[midway,above] {$w_{22}$};
    \draw[->] (i2) -- (h3) node[midway,above] {$w_{23}$};
    
    \draw[->] (i3) -- (h1);
    \draw[->] (i3) -- (h2);
    \draw[->] (i3) -- (h3);
    
    \draw[->] (i4) -- (h1);
    \draw[->] (i4) -- (h2);
    \draw[->] (i4) -- (h3);
    
    \draw[->] (h1) -- (o1) node[midway,above] {$v_1$};
    \draw[->] (h2) -- (o1) node[midway,above] {$v_2$};
    \draw[->] (h3) -- (o1) node[midway,above] {$v_3$};
    
    % Labels
    \node[below] at (0,-0.5) {Input Layer};
    \node[below] at (3,-0.5) {Hidden Layer};
    \node[below] at (6,-0.5) {Output Layer};
    
    % Input descriptions
    \node[left] at (-0.5,3) {Age};
    \node[left] at (-0.5,2) {Weight};
    \node[left] at (-0.5,1) {Creatinine};
    \node[left] at (-0.5,0) {Drug Level};
    
    % Output description
    \node[right] at (6.5,1.5) {Dose};
    
    % Mathematical representation
    \node[below,text width=10cm,align=center] at (3,-2) {
        $h_j = \sigma\left(\sum_{i=1}^{4} w_{ij}x_i + b_j\right)$ \quad 
        $y = \sum_{j=1}^{3} v_j h_j + c$
    };

\end{tikzpicture}
\caption{Neural Network Architecture for Dose Prediction: This diagram shows how patient characteristics (inputs) are processed through hidden layers using weighted connections to predict optimal dosing (output). The mathematical operations mirror the linear algebra concepts covered in earlier chapters.}
\label{fig:neural_network_architecture}
\end{figure>
