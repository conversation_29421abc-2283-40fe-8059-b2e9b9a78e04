% Matrix Operations and Eigenvalue Visualizations
% Advanced visual representations for linear algebra concepts

\usepackage{tikz}
\usepackage{pgfplots}
\pgfplotsset{compat=1.18}
\usetikzlibrary{arrows.meta,positioning,shapes.geometric,calc,decorations.pathreplacing,patterns}

% Eigenvalue and Eigenvector Visualizations

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.5]
    % Coordinate system
    \draw[->] (-2,0) -- (3,0) node[right] {$x$};
    \draw[->] (0,-2) -- (0,3) node[above] {$y$};
    
    % Original vectors (before transformation)
    \draw[->,thick,blue] (0,0) -- (1,0) node[below right] {$\vec{v_1}$};
    \draw[->,thick,blue] (0,0) -- (0,1) node[above left] {$\vec{v_2}$};
    \draw[->,thick,red] (0,0) -- (1,1) node[above right] {$\vec{u}$};
    
    % Transformed vectors (after transformation A)
    \draw[->,thick,blue,dashed] (0,0) -- (2,0) node[below right] {$A\vec{v_1} = 2\vec{v_1}$};
    \draw[->,thick,blue,dashed] (0,0) -- (0,0.5) node[above left] {$A\vec{v_2} = 0.5\vec{v_2}$};
    \draw[->,thick,red,dashed] (0,0) -- (1.5,1.5) node[above right] {$A\vec{u}$};
    
    % Eigenvalue annotations
    \node[blue] at (1,-0.5) {$\lambda_1 = 2$ (eigenvalue)};
    \node[blue] at (-1.5,1.5) {$\lambda_2 = 0.5$ (eigenvalue)};
    \node[red] at (2,0.5) {Not an eigenvector};
    
    % Matrix representation
    \node[below] at (0,-2.5) {$A = \begin{bmatrix} 2 & 0 \\ 0 & 0.5 \end{bmatrix}$};
    
    % Clinical interpretation
    \node[below,text width=12cm,align=center] at (0,-3.5) {
        \textbf{Pharmacological Interpretation:} Eigenvectors represent directions of pure drug action (e.g., receptor binding pathways). Eigenvalues represent the strength of action along each pathway. The transformation matrix A represents how a drug affects different biological pathways.
    };
\end{tikzpicture}
\caption{Eigenvalue and Eigenvector Visualization: Eigenvectors (blue) maintain their direction under linear transformation, only scaling by their eigenvalues. Non-eigenvectors (red) change both magnitude and direction. In pharmacology, eigenvectors represent principal modes of drug action.}
\label{fig:eigenvalue_visualization}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.2]
    % PCA visualization
    \begin{scope}
        % Data points (patient characteristics)
        \foreach \x/\y in {1/1.2, 1.5/1.8, 2/2.1, 2.5/2.8, 3/3.2, 1.8/1.5, 2.2/2.3, 2.8/2.9, 1.3/1.1, 2.7/3.1} {
            \fill[blue] (\x,\y) circle (0.05);
        }
        
        % Principal components
        \draw[->,very thick,red] (0.5,0.5) -- (3.5,3.8) node[above right] {PC1 (85\% variance)};
        \draw[->,very thick,green] (2,2) -- (1.2,2.8) node[above left] {PC2 (15\% variance)};
        
        % Coordinate system
        \draw[->] (0,0) -- (4,0) node[right] {Age (years)};
        \draw[->] (0,0) -- (0,4) node[above] {Weight (kg)};
        
        % Projections onto PC1
        \foreach \x/\y in {1/1.2, 1.5/1.8, 2/2.1, 2.5/2.8, 3/3.2} {
            \draw[dashed,gray] (\x,\y) -- ({0.5 + 0.707*(\x*0.707 + \y*0.707 - 0.5*0.707 - 0.5*0.707)}, {0.5 + 0.707*(\x*0.707 + \y*0.707 - 0.5*0.707 - 0.5*0.707)});
        }
        
        % Labels
        \node[blue] at (3.5,1) {Patient Data Points};
        \node[below] at (2,-0.5) {Original 2D Space};
    \end{scope}
    
    % Transformed space (1D projection)
    \begin{scope}[yshift=-6cm]
        \draw[->] (0,0) -- (4,0) node[right] {PC1 Score};
        \draw[->] (0,-0.5) -- (0,1) node[above] {};
        
        % Projected points
        \foreach \x in {0.8, 1.2, 1.6, 2.0, 2.4, 1.1, 1.7, 2.1, 0.9, 2.3} {
            \fill[blue] (\x,0) circle (0.05);
        }
        
        \node[below] at (2,-0.5) {Reduced 1D Space (Principal Component)};
        \node[blue] at (3.5,0.5) {Projected Patient Data};
    \end{scope}
    
    % Clinical interpretation
    \node[below,text width=12cm,align=center] at (2,-8) {
        \textbf{Clinical Application:} PCA reduces patient characteristics to principal components that capture most variation. PC1 might represent "overall patient size/age" while PC2 captures independent variation. This dimensionality reduction is fundamental to neural network preprocessing.
    };
\end{tikzpicture}
\caption{Principal Component Analysis (PCA): PCA finds the directions of maximum variance in patient data. The first principal component (PC1) captures the most variation, allowing dimensionality reduction while preserving information. This technique is essential for preprocessing high-dimensional pharmaceutical data.}
\label{fig:pca_visualization}
\end{figure}

% Matrix Decomposition Visualizations

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.8]
    % SVD decomposition visualization
    \node at (-4,2) {$A$};
    \draw[thick] (-4.5,1.5) rectangle (-3.5,2.5);
    \fill[blue!30] (-4.5,1.5) rectangle (-3.5,2.5);
    \node[below] at (-4,1.2) {$m \times n$};
    
    \node at (-1,0) {$=$};
    
    % U matrix
    \node at (0,2) {$U$};
    \draw[thick] (-0.5,1.5) rectangle (0.5,2.5);
    \fill[red!30] (-0.5,1.5) rectangle (0.5,2.5);
    \node[below] at (0,1.2) {$m \times m$};
    
    % Sigma matrix
    \node at (2,2) {$\Sigma$};
    \draw[thick] (1.5,1.5) rectangle (2.5,2.5);
    \fill[green!30] (1.5,1.5) rectangle (2.5,2.5);
    % Diagonal pattern
    \draw[thick] (1.6,1.6) -- (2.4,2.4);
    \node[below] at (2,1.2) {$m \times n$};
    
    % V^T matrix
    \node at (4,2) {$V^T$};
    \draw[thick] (3.5,1.5) rectangle (4.5,2.5);
    \fill[yellow!30] (3.5,1.5) rectangle (4.5,2.5);
    \node[below] at (4,1.2) {$n \times n$};
    
    % Interpretation boxes
    \node[below,text width=3cm,align=center] at (0,0.5) {
        \textbf{Left Singular Vectors}\\
        Patient patterns
    };
    
    \node[below,text width=3cm,align=center] at (2,0.5) {
        \textbf{Singular Values}\\
        Pattern strengths
    };
    
    \node[below,text width=3cm,align=center] at (4,0.5) {
        \textbf{Right Singular Vectors}\\
        Drug patterns
    };
    
    % Clinical example
    \node[below,text width=12cm,align=center] at (1,-1.5) {
        \textbf{Pharmaceutical Application:} SVD decomposes a patient-drug response matrix into patient patterns (U), pattern importance (Σ), and drug patterns (V). This reveals hidden relationships between patient characteristics and drug responses, enabling personalized medicine approaches.
    };
\end{tikzpicture}
\caption{Singular Value Decomposition (SVD): SVD factorizes any matrix into three components representing orthogonal patterns in the data. In pharmaceutical applications, this reveals latent relationships between patients and drug responses, forming the basis for recommendation systems and personalized dosing algorithms.}
\label{fig:svd_visualization}
\end{figure}

% Linear System Visualization

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % 2x2 system visualization
    \begin{axis}[
        width=10cm,
        height=8cm,
        xlabel={Drug A Concentration (mg/L)},
        ylabel={Drug B Concentration (mg/L)},
        xmin=-1, xmax=6,
        ymin=-1, ymax=6,
        grid=major,
        axis equal,
        title={Linear System: Drug Interaction Model}
    ]
    
    % First equation: 2x + y = 4
    \addplot[blue,thick,domain=-1:6] {4-2*x};
    \addlegendentry{$2C_A + C_B = 4$ (Efficacy constraint)};
    
    % Second equation: x + 3y = 6  
    \addplot[red,thick,domain=-1:6] {(6-x)/3};
    \addlegendentry{$C_A + 3C_B = 6$ (Safety constraint)};
    
    % Solution point
    \addplot[mark=*,mark size=4pt,green] coordinates {(1.2,1.6)};
    \node[green,above right] at (axis cs:1.2,1.6) {Solution: $(1.2, 1.6)$};
    
    % Feasible region
    \fill[green,opacity=0.2] (axis cs:0,0) -- (axis cs:0,2) -- (axis cs:1.2,1.6) -- (axis cs:2,0) -- cycle;
    \node[green] at (axis cs:0.8,0.8) {Feasible Region};
    
    \end{axis}
    
    % Matrix representation
    \node[below] at (5,-1) {
        $\begin{bmatrix} 2 & 1 \\ 1 & 3 \end{bmatrix} \begin{bmatrix} C_A \\ C_B \end{bmatrix} = \begin{bmatrix} 4 \\ 6 \end{bmatrix}$
    };
    
    % Clinical interpretation
    \node[below,text width=12cm,align=center] at (5,-2.5) {
        \textbf{Clinical Interpretation:} The intersection point represents optimal drug concentrations that satisfy both efficacy and safety constraints. The feasible region shows all acceptable concentration combinations. This geometric approach underlies optimization algorithms used in neural networks.
    };
\end{tikzpicture}
\caption{Linear System Geometric Solution: Two linear equations represent clinical constraints (efficacy and safety). Their intersection gives the optimal drug concentration combination. The geometric visualization helps understand how multiple constraints interact in pharmaceutical optimization problems.}
\label{fig:linear_system}
\end{figure>

% Transformation Composition Visualization

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.9]
    % Original shape
    \begin{scope}[xshift=0cm]
        \draw[thick,blue,fill=blue!20] (0,0) -- (1,0) -- (1,1) -- (0,1) -- cycle;
        \node[below] at (0.5,-0.5) {Original};
        \node[blue] at (0.5,0.5) {$\vec{x}$};
    \end{scope}
    
    % First transformation
    \begin{scope}[xshift=3cm]
        \draw[thick,red,fill=red!20] (0,0) -- (2,0) -- (2,2) -- (0,2) -- cycle;
        \node[below] at (1,-0.5) {Scale by 2};
        \node[red] at (1,1) {$T_1(\vec{x})$};
        \node[above] at (1,2.5) {$T_1 = \begin{bmatrix} 2 & 0 \\ 0 & 2 \end{bmatrix}$};
    \end{scope}
    
    % Second transformation  
    \begin{scope}[xshift=7cm]
        \draw[thick,green,fill=green!20] (0,0) -- (2,1) -- (2,3) -- (0,2) -- cycle;
        \node[below] at (1,-0.5) {Shear};
        \node[green] at (1,1.5) {$T_2(T_1(\vec{x}))$};
        \node[above] at (1,3.5) {$T_2 = \begin{bmatrix} 1 & 0.5 \\ 0 & 1 \end{bmatrix}$};
    \end{scope}
    
    % Arrows
    \draw[->,thick] (1.2,0.5) -- (2.8,0.5);
    \draw[->,thick] (5.2,1) -- (6.8,1);
    
    % Composition
    \node[below] at (3.5,-2) {
        $T_2 \circ T_1 = \begin{bmatrix} 1 & 0.5 \\ 0 & 1 \end{bmatrix} \begin{bmatrix} 2 & 0 \\ 0 & 2 \end{bmatrix} = \begin{bmatrix} 2 & 1 \\ 0 & 2 \end{bmatrix}$
    };
    
    % Neural network connection
    \node[below,text width=12cm,align=center] at (3.5,-3.5) {
        \textbf{Neural Network Connection:} Function composition in neural networks works similarly - each layer applies a linear transformation followed by a non-linear activation. The composition of simple transformations creates complex mappings from inputs to outputs.
    };
\end{tikzpicture}
\caption{Matrix Transformation Composition: Sequential transformations combine through matrix multiplication. First, scaling enlarges the shape uniformly. Then, shearing distorts it. The final transformation is the product of the individual transformation matrices, demonstrating how neural network layers compose functions.}
\label{fig:transformation_composition}
\end{figure>

% Optimization Landscape Visualization

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=12cm,
        height=8cm,
        xlabel={Parameter 1 (e.g., Learning Rate)},
        ylabel={Parameter 2 (e.g., Regularization)},
        zlabel={Cost Function},
        view={45}{30},
        colormap/viridis,
        title={Optimization Landscape for Neural Network Training}
    ]
    
    % 3D surface representing cost function
    \addplot3[
        surf,
        domain=-2:2,
        domain y=-2:2,
        samples=25,
        opacity=0.8
    ] {x^2 + y^2 + 0.5*sin(deg(3*x))*cos(deg(3*y))};
    
    % Global minimum
    \addplot3[mark=*,mark size=4pt,red] coordinates {(0,0,0)};
    
    % Gradient descent path
    \addplot3[thick,blue,mark=*,mark size=2pt] coordinates {
        (1.5,1.5,4.5) (1.2,1.2,2.88) (0.9,0.9,1.62) (0.6,0.6,0.72) (0.3,0.3,0.18) (0,0,0)
    };
    
    \end{axis}
    
    % Annotations
    \node[red] at (6,6) {Global Minimum};
    \node[blue] at (9,7) {Gradient Descent Path};
    
    % Clinical interpretation
    \node[below,text width=12cm,align=center] at (6,-1) {
        \textbf{Pharmaceutical Optimization:} The surface represents how prediction error changes with model parameters. Neural networks use gradient descent to find the minimum error configuration, similar to how pharmacologists optimize drug dosing to minimize adverse effects while maximizing efficacy.
    };
\end{tikzpicture}
\caption{Optimization Landscape: The 3D surface shows how a cost function varies with two parameters. Neural networks navigate this landscape using gradient descent (blue path) to find optimal parameters (red point). The geometric intuition helps understand why optimization can be challenging in high-dimensional parameter spaces.}
\label{fig:optimization_landscape}
\end{figure>