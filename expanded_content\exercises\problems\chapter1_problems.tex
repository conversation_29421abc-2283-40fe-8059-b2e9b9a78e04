% Chapter 1 Practice Problems
% Introduction to Neural Networks in Pharmacology

\section{Chapter 1 Practice Problems}

\subsection{Problems for Section 1.1: Mathematical Foundation of Modern Pharmacology}

\begin{problem}{1.1.1}{Basic Therapeutic Index Calculation}
A new antiarrhythmic drug shows the following toxicity and efficacy data from clinical trials:
\begin{itemize}
\item $TD_{50}$ (toxic dose for 50\% of patients) = 150 mg
\item $ED_{50}$ (effective dose for 50\% of patients) = 25 mg
\end{itemize}

\textbf{Part A:} Calculate the therapeutic index for this drug.

\textbf{Part B:} Compare this to warfarin (TI ≈ 2) and acetaminophen (TI ≈ 10). What does this tell you about the safety profile?

\textbf{Part C:} If the effective dose range is 20-30 mg and toxic effects begin at 120 mg, calculate the therapeutic window in mg.
\end{problem}

\begin{problem}{1.1.2}{Michaelis-Menten Kinetics Application}
A patient is taking a medication that follows Michaelis-Menten elimination kinetics with the following parameters:
\begin{itemize}
\item $V_{max}$ = 400 mg/day (maximum elimination rate)
\item $K_m$ = 50 mg/L (concentration at half-maximum velocity)
\item Current plasma concentration = 75 mg/L
\end{itemize}

\textbf{Part A:} Calculate the current elimination rate.

\textbf{Part B:} If the concentration drops to 25 mg/L, what will be the new elimination rate?

\textbf{Part C:} Explain why this non-linear relationship is important for dosing adjustments.
\end{problem}

\begin{problem}{1.1.3}{Hill Equation and Dose-Response}
A new diabetes medication shows the following dose-response relationship:
\begin{itemize}
\item Maximum response ($R_{max}$) = 80\% reduction in blood glucose
\item $EC_{50}$ = 10 mg (dose producing 50\% of maximum response)
\item Hill coefficient ($n$) = 2.5
\end{itemize}

\textbf{Part A:} Write the complete Hill equation for this drug.

\textbf{Part B:} Calculate the response at doses of 5 mg, 10 mg, and 20 mg.

\textbf{Part C:} What does the Hill coefficient of 2.5 tell you about the cooperativity of drug binding?
\end{problem}

\begin{problem}{1.1.4}{Two-Compartment Model Parameters}
A drug follows two-compartment kinetics with these parameters:
\begin{itemize}
\item $k_{12}$ = 0.5 hr$^{-1}$ (central to peripheral transfer)
\item $k_{21}$ = 0.2 hr$^{-1}$ (peripheral to central transfer)
\item $k_{10}$ = 0.3 hr$^{-1}$ (elimination from central)
\item $V_1$ = 10 L (central volume)
\item $V_2$ = 30 L (peripheral volume)
\end{itemize}

\textbf{Part A:} Calculate the distribution half-life ($t_{1/2,\alpha}$) and elimination half-life ($t_{1/2,\beta}$).

\textbf{Part B:} If 500 mg is given as an IV bolus, predict the plasma concentration at 1 hour and 6 hours.

\textbf{Part C:} Calculate the steady-state volume of distribution ($V_{ss}$).
\end{problem}

\begin{problem}{1.1.5}{Population Pharmacokinetics Variability}
A population pharmacokinetic study of a new antibiotic shows:
\begin{itemize}
\item Population clearance ($CL_{pop}$) = 5 L/hr
\item Inter-individual variability in clearance (CV\%) = 40\%
\item Population volume ($V_{pop}$) = 50 L
\item Inter-individual variability in volume (CV\%) = 25\%
\end{itemize}

\textbf{Part A:} For a patient with $\eta_{CL} = 0.3$ and $\eta_V = -0.2$, calculate their individual clearance and volume.

\textbf{Part B:} Calculate the elimination half-life for this individual patient.

\textbf{Part C:} What dose would achieve a steady-state concentration of 10 mg/L in this patient?
\end{problem}

\subsection{Problems for Section 1.2: Real-World Applications in Clinical Pharmacology}

\begin{problem}{1.2.1}{Drug Discovery Pattern Recognition}
A pharmaceutical company is analyzing molecular descriptors for potential drug candidates. They have calculated Tanimoto coefficients between a reference compound and three candidates:
\begin{itemize}
\item Compound A: 147 common features, 203 total features in A, 189 total features in reference
\item Compound B: 89 common features, 156 total features in B, 189 total features in reference
\item Compound C: 178 common features, 234 total features in C, 189 total features in reference
\end{itemize}

\textbf{Part A:} Calculate the Tanimoto coefficient for each compound.

\textbf{Part B:} Rank the compounds by similarity to the reference.

\textbf{Part C:} If compounds with Tanimoto coefficient > 0.7 are considered "similar," which compounds qualify for further testing?
\end{problem}

\begin{problem}{1.2.2}{GAN Objective Function Analysis}
A generative adversarial network (GAN) is being trained to generate new molecular structures. The current loss values are:
\begin{itemize}
\item Generator loss: $L_G = 2.3$
\item Discriminator loss: $L_D = 0.8$
\item Training iteration: 1000
\end{itemize}

\textbf{Part A:} Interpret these loss values. Is the generator or discriminator performing better?

\textbf{Part B:} If the ideal scenario has both losses around 0.693 (ln(2)), what adjustments might be needed?

\textbf{Part C:} Calculate the total adversarial loss: $L_{total} = L_G + L_D$.
\end{problem}

\begin{problem}{1.2.3}{Clinical Decision Support System}
A neural network-based clinical decision support system for anticoagulation therapy uses the following patient features:
\begin{itemize}
\item Age: 65 years (normalized: 0.65)
\item Weight: 80 kg (normalized: 0.8)
\item Creatinine clearance: 60 mL/min (normalized: 0.6)
\item CYP2C9 genotype score: 0.75
\item VKORC1 genotype score: 0.9
\end{itemize}

The first layer weights for warfarin dose prediction are:
$\mathbf{W}_1 = \begin{bmatrix} 0.3 & 0.5 & -0.4 & 0.2 & -0.3 \end{bmatrix}$

\textbf{Part A:} Calculate the weighted sum for the first neuron.

\textbf{Part B:} If the bias term is $b_1 = 0.1$, what is the pre-activation value?

\textbf{Part C:} Apply the sigmoid activation function to get the final output.
\end{problem}

\subsection{Problems for Section 1.3: Mathematical Prerequisites Review}

\begin{problem}{1.3.1}{Logarithms in Pharmacokinetics}
A drug follows first-order elimination kinetics with a half-life of 6 hours.

\textbf{Part A:} Calculate the elimination rate constant ($k$) using the relationship $t_{1/2} = \frac{\ln(2)}{k}$.

\textbf{Part B:} If the initial concentration is 100 mg/L, write the equation for concentration as a function of time.

\textbf{Part C:} How long will it take for the concentration to drop to 12.5 mg/L?

\textbf{Part D:} Express your answer in terms of half-lives and verify using logarithms.
\end{problem}

\begin{problem}{1.3.2}{Exponential Functions in Drug Release}
A controlled-release tablet follows zero-order release kinetics for the first 8 hours, then first-order release. The release profile is:
\begin{itemize}
\item Zero-order phase: $A(t) = 100 - 10t$ mg (for $t \leq 8$ hours)
\item First-order phase: $A(t) = 20e^{-0.1(t-8)}$ mg (for $t > 8$ hours)
\end{itemize}

\textbf{Part A:} Calculate the amount remaining at $t = 4$ hours and $t = 12$ hours.

\textbf{Part B:} Verify continuity at $t = 8$ hours.

\textbf{Part C:} Calculate the release rate at $t = 6$ hours and $t = 10$ hours.
\end{problem}

\begin{problem}{1.3.3}{Unit Conversions in Clinical Practice}
A pediatric patient requires a medication dose based on body surface area.

Given information:
\begin{itemize}
\item Patient weight: 25 kg
\item Patient height: 110 cm
\item Adult dose: 150 mg/m²
\item Available concentration: 50 mg/5 mL
\end{itemize}

\textbf{Part A:} Calculate the body surface area using the Mosteller formula: $BSA = \sqrt{\frac{height(cm) \times weight(kg)}{3600}}$

\textbf{Part B:} Calculate the required dose in mg.

\textbf{Part C:} Convert to the volume needed in mL.

\textbf{Part D:} If the dose is given twice daily, calculate the daily volume requirement.
\end{problem}

\subsection{Problems for Section 1.4: Data Representation in Pharmacology}

\begin{problem}{1.4.1}{Patient Data Vectorization}
Convert the following patient information into a mathematical vector for neural network input:

Patient Profile:
\begin{itemize}
\item Age: 45 years
\item Weight: 70 kg
\item Height: 170 cm
\item Systolic BP: 140 mmHg
\item Diastolic BP: 90 mmHg
\item Creatinine: 1.2 mg/dL
\item Diabetes: Yes
\item Smoking: No
\end{itemize}

Normalization ranges:
\begin{itemize}
\item Age: 0-100 years → [0,1]
\item Weight: 40-120 kg → [0,1]
\item Height: 140-200 cm → [0,1]
\item Systolic BP: 80-200 mmHg → [0,1]
\item Diastolic BP: 50-120 mmHg → [0,1]
\item Creatinine: 0.5-3.0 mg/dL → [0,1]
\item Binary variables: Yes=1, No=0
\end{itemize}

\textbf{Part A:} Create the normalized patient vector $\mathbf{x}$.

\textbf{Part B:} Calculate the Euclidean norm of this vector.

\textbf{Part C:} If another patient has vector $\mathbf{y} = [0.6, 0.8, 0.7, 0.5, 0.4, 0.3, 0, 1]$, calculate the cosine similarity between the two patients.
\end{problem}

\begin{problem}{1.4.2}{Drug Property Matrix Construction}
Create a drug property matrix for three medications:

\begin{center}
\begin{tabular}{|l|c|c|c|c|}
\hline
Drug & LogP & Molecular Weight & H-bond Donors & H-bond Acceptors \\
\hline
Aspirin & 1.19 & 180.16 & 1 & 4 \\
Ibuprofen & 3.97 & 206.28 & 1 & 2 \\
Acetaminophen & 0.46 & 151.16 & 2 & 3 \\
\hline
\end{tabular}
\end{center}

\textbf{Part A:} Construct the $3 \times 4$ drug property matrix $\mathbf{D}$.

\textbf{Part B:} Normalize each column to have values between 0 and 1.

\textbf{Part C:} Calculate the correlation matrix between the four properties.

\textbf{Part D:} Which two properties are most strongly correlated?
\end{problem}

\begin{problem}{1.4.3}{Clinical Trial Dataset Structure}
A clinical trial dataset contains the following variables for 100 patients:
\begin{itemize}
\item Demographics: Age, Weight, Height, Gender (4 variables)
\item Baseline labs: 8 laboratory values
\item Genetic markers: 5 SNP variants (binary)
\item Treatment response: Continuous efficacy score
\item Adverse events: 3 binary indicators
\end{itemize}

\textbf{Part A:} What are the dimensions of the complete dataset matrix?

\textbf{Part B:} If we separate features (X) and outcomes (Y), what are their respective dimensions?

\textbf{Part C:} Calculate the total number of data points in the dataset.

\textbf{Part D:} If 20\% of data is reserved for testing, how many samples are available for training?
\end{problem}

\subsection{Problems for Section 1.5: Pattern Recognition in Clinical Practice}

\begin{problem}{1.5.1}{Clinical Pattern Similarity}
A diagnostic system compares patient symptoms to known disease patterns. Consider these symptom vectors (1=present, 0=absent):

\begin{itemize}
\item Patient A: [1, 1, 0, 1, 0, 1, 0, 1]
\item Disease Pattern 1: [1, 1, 1, 1, 0, 0, 0, 1]
\item Disease Pattern 2: [0, 1, 0, 1, 1, 1, 0, 0]
\item Disease Pattern 3: [1, 0, 0, 1, 0, 1, 1, 1]
\end{itemize}

\textbf{Part A:} Calculate the Hamming distance between Patient A and each disease pattern.

\textbf{Part B:} Calculate the Jaccard similarity coefficient for each comparison.

\textbf{Part C:} Which disease pattern is most similar to Patient A's presentation?

\textbf{Part D:} If the diagnostic threshold requires Jaccard similarity > 0.6, which patterns qualify?
\end{problem}

\begin{problem}{1.5.2}{Drug Response Pattern Classification}
A pharmacogenomics study identified response patterns for a cardiovascular drug:

Response Pattern A (Good responders): Mean vector = [0.8, 0.3, 0.9, 0.2]
Response Pattern B (Poor responders): Mean vector = [0.2, 0.7, 0.1, 0.8]

New patient vector: [0.7, 0.4, 0.8, 0.3]

\textbf{Part A:} Calculate the Euclidean distance to each response pattern.

\textbf{Part B:} Calculate the Manhattan distance to each response pattern.

\textbf{Part C:} Using nearest neighbor classification, predict the patient's response category.

\textbf{Part D:} Calculate the confidence of this prediction using the ratio of distances.
\end{problem}

\begin{problem}{1.5.3}{Adverse Event Pattern Detection}
A safety monitoring system tracks adverse event patterns. The following represents occurrence rates (per 1000 patients) for different drug combinations:

\begin{center}
\begin{tabular}{|l|c|c|c|c|}
\hline
Drug Combination & Nausea & Dizziness & Rash & Fatigue \\
\hline
A + B & 45 & 23 & 12 & 67 \\
A + C & 12 & 8 & 34 & 23 \\
B + C & 78 & 45 & 6 & 89 \\
A + B + C & 89 & 67 & 45 & 123 \\
\hline
\end{tabular}
\end{center}

\textbf{Part A:} Normalize the data to create probability vectors for each combination.

\textbf{Part B:} Calculate the correlation between nausea and fatigue across all combinations.

\textbf{Part C:} Identify which drug combination has the most similar adverse event profile to A + B + C.

\textbf{Part D:} Calculate the total adverse event burden for each combination.
\end{problem}