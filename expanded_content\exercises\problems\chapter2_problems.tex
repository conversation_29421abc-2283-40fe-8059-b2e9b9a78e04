% Chapter 2 Practice Problems
% Essential Linear Algebra for Drug Data

\section{Chapter 2 Practice Problems}

\subsection{Problems for Section 2.1: Vectors in Pharmaceutical Context}

\begin{problem}{2.1.1}{Drug Concentration Vectors}
A patient is taking three medications with the following plasma concentrations measured at different time points:

Time 0h: $\mathbf{c}_0 = \begin{bmatrix} 50 \\ 25 \\ 10 \end{bmatrix}$ mg/L

Time 6h: $\mathbf{c}_6 = \begin{bmatrix} 35 \\ 18 \\ 7 \end{bmatrix}$ mg/L

\textbf{Part A:} Calculate the concentration change vector $\Delta\mathbf{c} = \mathbf{c}_6 - \mathbf{c}_0$.

\textbf{Part B:} Calculate the magnitude of the concentration change using the Euclidean norm.

\textbf{Part C:} Calculate the unit vector in the direction of concentration change.

\textbf{Part D:} If the therapeutic target vector is $\mathbf{t} = \begin{bmatrix} 40 \\ 20 \\ 8 \end{bmatrix}$ mg/L, calculate the cosine similarity between $\mathbf{c}_6$ and $\mathbf{t}$.
\end{problem}

\begin{problem}{2.1.2}{Patient Similarity Analysis}
Two patients have the following clinical parameter vectors:

Patient 1: $\mathbf{p}_1 = \begin{bmatrix} 0.65 \\ 0.8 \\ 0.45 \\ 0.9 \\ 0.3 \end{bmatrix}$ (normalized values)

Patient 2: $\mathbf{p}_2 = \begin{bmatrix} 0.72 \\ 0.75 \\ 0.52 \\ 0.85 \\ 0.28 \end{bmatrix}$ (normalized values)

\textbf{Part A:} Calculate the dot product $\mathbf{p}_1 \cdot \mathbf{p}_2$.

\textbf{Part B:} Calculate the Euclidean distance between the patients.

\textbf{Part C:} Calculate the cosine similarity between the patients.

\textbf{Part D:} If cosine similarity > 0.95 indicates "similar patients," are these patients similar enough for the same treatment protocol?
\end{problem}

\begin{problem}{2.1.3}{Drug Efficacy Vector Operations}
A clinical trial measures drug efficacy across five endpoints. The efficacy vectors for two treatment arms are:

Treatment A: $\mathbf{a} = \begin{bmatrix} 0.8 \\ 0.6 \\ 0.9 \\ 0.7 \\ 0.5 \end{bmatrix}$

Treatment B: $\mathbf{b} = \begin{bmatrix} 0.7 \\ 0.8 \\ 0.6 \\ 0.9 \\ 0.4 \end{bmatrix}$

\textbf{Part A:} Calculate the combined efficacy vector $\mathbf{c} = 0.6\mathbf{a} + 0.4\mathbf{b}$ (weighted combination).

\textbf{Part B:} Calculate the L1 norm (Manhattan distance) of the difference vector $\mathbf{a} - \mathbf{b}$.

\textbf{Part C:} Calculate the L2 norm (Euclidean distance) of the difference vector.

\textbf{Part D:} Which treatment shows higher overall efficacy based on the L2 norm of each vector?
\end{problem}

\begin{problem}{2.1.4}{Pharmacokinetic Parameter Vectors}
A population pharmacokinetic study yields the following parameter vectors for clearance, volume, and absorption rate:

$\mathbf{CL} = \begin{bmatrix} 5.2 \\ 4.8 \\ 6.1 \\ 5.5 \\ 4.9 \end{bmatrix}$ L/hr

$\mathbf{V} = \begin{bmatrix} 45 \\ 52 \\ 38 \\ 48 \\ 50 \end{bmatrix}$ L

$\mathbf{k_a} = \begin{bmatrix} 1.2 \\ 0.9 \\ 1.5 \\ 1.1 \\ 1.3 \end{bmatrix}$ hr$^{-1}$

\textbf{Part A:} Calculate the elimination half-life vector $\mathbf{t}_{1/2} = \frac{0.693 \mathbf{V}}{\mathbf{CL}}$ (element-wise division).

\textbf{Part B:} Calculate the mean and standard deviation of the clearance vector.

\textbf{Part C:} Normalize the clearance vector to have zero mean and unit variance.

\textbf{Part D:} Calculate the correlation coefficient between clearance and volume vectors.
\end{problem}

\begin{problem}{2.1.5}{Dose Optimization Vector Problem}
A multi-drug regimen requires optimization of doses for three medications. The current dose vector is:

$\mathbf{d}_{current} = \begin{bmatrix} 100 \\ 50 \\ 25 \end{bmatrix}$ mg

The target efficacy vector is:

$\mathbf{e}_{target} = \begin{bmatrix} 0.8 \\ 0.7 \\ 0.9 \end{bmatrix}$

The dose-response sensitivity vector is:

$\mathbf{s} = \begin{bmatrix} 0.006 \\ 0.012 \\ 0.030 \end{bmatrix}$ (efficacy per mg)

\textbf{Part A:} Calculate the current predicted efficacy vector $\mathbf{e}_{current} = \mathbf{s} \odot \mathbf{d}_{current}$ (element-wise multiplication).

\textbf{Part B:} Calculate the efficacy deficit vector $\Delta\mathbf{e} = \mathbf{e}_{target} - \mathbf{e}_{current}$.

\textbf{Part C:} Calculate the required dose adjustment vector $\Delta\mathbf{d} = \frac{\Delta\mathbf{e}}{\mathbf{s}}$ (element-wise division).

\textbf{Part D:} Calculate the new optimized dose vector $\mathbf{d}_{new} = \mathbf{d}_{current} + \Delta\mathbf{d}$.
\end{problem}

\subsection{Problems for Section 2.2: Matrices - Organizing Multi-dimensional Data}

\begin{problem}{2.2.1}{Clinical Trial Data Matrix}
A clinical trial collects data on 4 patients across 3 biomarkers:

$\mathbf{X} = \begin{bmatrix}
12.5 & 8.2 & 145 \\
15.1 & 9.8 & 132 \\
11.8 & 7.5 & 158 \\
13.9 & 8.9 & 140
\end{bmatrix}$

Where columns represent: Hemoglobin (g/dL), White Blood Cells (×10³/μL), Platelets (×10³/μL)

\textbf{Part A:} Calculate the mean vector for each biomarker (column means).

\textbf{Part B:} Calculate the patient similarity matrix using dot products between row vectors.

\textbf{Part C:} Standardize the matrix by subtracting column means and dividing by column standard deviations.

\textbf{Part D:} Calculate the covariance matrix of the standardized data.
\end{problem}

\begin{problem}{2.2.2}{Drug Interaction Matrix}
A drug interaction study examines the effects of three drugs (A, B, C) on three enzymes (CYP1A2, CYP2D6, CYP3A4). The inhibition matrix is:

$\mathbf{I} = \begin{bmatrix}
0.2 & 0.8 & 0.1 \\
0.9 & 0.3 & 0.4 \\
0.1 & 0.6 & 0.9
\end{bmatrix}$

Where $I_{ij}$ represents the inhibition of enzyme $j$ by drug $i$ (0 = no inhibition, 1 = complete inhibition).

\textbf{Part A:} Which drug is the strongest inhibitor of CYP2D6?

\textbf{Part B:} Calculate the total inhibition score for each drug (row sums).

\textbf{Part C:} If a patient takes drugs A and B simultaneously, calculate the combined inhibition vector assuming additive effects.

\textbf{Part D:} Calculate the transpose $\mathbf{I}^T$ and interpret its meaning.
\end{problem}

\begin{problem}{2.2.3}{Pharmacokinetic Matrix Operations}
A population pharmacokinetic model uses the following matrices:

Design matrix: $\mathbf{X} = \begin{bmatrix}
1 & 70 & 45 \\
1 & 85 & 52 \\
1 & 62 & 38 \\
1 & 78 & 49
\end{bmatrix}$ (intercept, weight, age)

Parameter vector: $\boldsymbol{\beta} = \begin{bmatrix} 2.5 \\ 0.08 \\ -0.02 \end{bmatrix}$ (clearance model)

\textbf{Part A:} Calculate the predicted clearance vector $\mathbf{CL} = \mathbf{X}\boldsymbol{\beta}$.

\textbf{Part B:} Calculate $\mathbf{X}^T\mathbf{X}$ (information matrix).

\textbf{Part C:} If the observed clearances are $\mathbf{CL}_{obs} = \begin{bmatrix} 8.2 \\ 9.1 \\ 6.8 \\ 8.7 \end{bmatrix}$, calculate the residual vector.

\textbf{Part D:} Calculate the sum of squared residuals.
\end{problem}

\begin{problem}{2.2.4}{Dose-Response Matrix Analysis}
A dose-response study tests 3 doses of a drug on 4 different cell lines:

$\mathbf{R} = \begin{bmatrix}
0.1 & 0.3 & 0.8 \\
0.2 & 0.5 & 0.9 \\
0.05 & 0.2 & 0.6 \\
0.15 & 0.4 & 0.85
\end{bmatrix}$

Where columns represent doses (10, 50, 200 μM) and rows represent cell lines.

\textbf{Part A:} Calculate the dose-response slope for each cell line using the formula: slope = $\frac{R_{high} - R_{low}}{dose_{high} - dose_{low}}$.

\textbf{Part B:} Create a sensitivity ranking matrix by ranking cell lines (1-4) for each dose.

\textbf{Part C:} Calculate the correlation matrix between the three dose columns.

\textbf{Part D:} If the matrix is multiplied by a dose weighting vector $\mathbf{w} = \begin{bmatrix} 0.2 \\ 0.3 \\ 0.5 \end{bmatrix}$, calculate the weighted response vector for each cell line.
\end{problem}

\begin{problem}{2.2.5}{Multi-Drug Combination Matrix}
A combination therapy study examines the interaction between two drugs across different concentration ratios:

Drug A concentrations: [1, 2, 4, 8] μM
Drug B concentrations: [0.5, 1, 2, 4] μM

The interaction matrix shows fractional inhibition:

$\mathbf{F} = \begin{bmatrix}
0.1 & 0.2 & 0.4 & 0.6 \\
0.2 & 0.35 & 0.55 & 0.75 \\
0.3 & 0.5 & 0.7 & 0.85 \\
0.4 & 0.6 & 0.8 & 0.95
\end{bmatrix}$

\textbf{Part A:} Calculate the expected additive effect matrix assuming $F_{add} = F_A + F_B - F_A \cdot F_B$.

\textbf{Part B:} Calculate the interaction index matrix $II = \frac{F_{observed}}{F_{additive}}$.

\textbf{Part C:} Identify synergistic (II < 0.9), additive (0.9 ≤ II ≤ 1.1), and antagonistic (II > 1.1) combinations.

\textbf{Part D:} Calculate the maximum synergistic effect and its corresponding concentration combination.
\end{problem}

\subsection{Problems for Section 2.3: Matrix Decomposition Techniques}

\begin{problem}{2.3.1}{LU Decomposition for Pharmacokinetic Systems}
A three-compartment pharmacokinetic model yields the following coefficient matrix:

$\mathbf{A} = \begin{bmatrix}
-0.5 & 0.2 & 0.1 \\
0.3 & -0.8 & 0.0 \\
0.1 & 0.0 & -0.3
\end{bmatrix}$

\textbf{Part A:} Perform LU decomposition to find matrices $\mathbf{L}$ and $\mathbf{U}$.

\textbf{Part B:} Verify your decomposition by calculating $\mathbf{L}\mathbf{U}$.

\textbf{Part C:} Use the LU decomposition to solve $\mathbf{A}\mathbf{x} = \mathbf{b}$ where $\mathbf{b} = \begin{bmatrix} 100 \\ 0 \\ 0 \end{bmatrix}$.

\textbf{Part D:} Interpret the solution in terms of initial drug distribution.
\end{problem}

\begin{problem}{2.3.2}{QR Decomposition for Clinical Data}
A clinical dataset matrix needs QR decomposition for regression analysis:

$\mathbf{X} = \begin{bmatrix}
1 & 2 \\
1 & 1 \\
1 & 3
\end{bmatrix}$

\textbf{Part A:} Calculate the QR decomposition using the Gram-Schmidt process.

\textbf{Part B:} Verify that $\mathbf{Q}^T\mathbf{Q} = \mathbf{I}$.

\textbf{Part C:} Use the QR decomposition to solve the least squares problem $\mathbf{X}\boldsymbol{\beta} = \mathbf{y}$ where $\mathbf{y} = \begin{bmatrix} 5 \\ 3 \\ 7 \end{bmatrix}$.

\textbf{Part D:} Calculate the coefficient of determination ($R^2$) for this regression.
\end{problem}

\begin{problem}{2.3.3}{SVD for Drug Discovery Data}
A drug-target interaction matrix undergoes singular value decomposition:

$\mathbf{M} = \begin{bmatrix}
4 & 2 & 1 \\
2 & 3 & 2 \\
1 & 2 & 4
\end{bmatrix}$

Given that the SVD is $\mathbf{M} = \mathbf{U}\boldsymbol{\Sigma}\mathbf{V}^T$ with singular values $\sigma_1 = 6.2$, $\sigma_2 = 2.8$, $\sigma_3 = 0.5$.

\textbf{Part A:} Calculate the rank-2 approximation $\mathbf{M}_2$ using the first two singular values.

\textbf{Part B:} Calculate the Frobenius norm of the approximation error $\|\mathbf{M} - \mathbf{M}_2\|_F$.

\textbf{Part C:} What percentage of the total variance is captured by the rank-2 approximation?

\textbf{Part D:} Interpret the biological meaning of dimensionality reduction in drug-target interactions.
\end{problem}

\subsection{Problems for Section 2.4: Eigenvalues and Eigenvectors}

\begin{problem}{2.4.1}{Pharmacokinetic Compartmental Analysis}
A two-compartment pharmacokinetic model has the system matrix:

$\mathbf{K} = \begin{bmatrix}
-0.6 & 0.2 \\
0.4 & -0.3
\end{bmatrix}$

\textbf{Part A:} Calculate the eigenvalues of matrix $\mathbf{K}$.

\textbf{Part B:} Calculate the corresponding eigenvectors.

\textbf{Part C:} Interpret the eigenvalues as elimination rate constants and calculate the corresponding half-lives.

\textbf{Part D:} If the initial condition is $\mathbf{A}(0) = \begin{bmatrix} 100 \\ 0 \end{bmatrix}$ mg, express the solution in terms of eigenvalues and eigenvectors.
\end{problem}

\begin{problem}{2.4.2}{Principal Component Analysis of Clinical Data}
A clinical dataset has the following covariance matrix for three biomarkers:

$\mathbf{C} = \begin{bmatrix}
4.0 & 2.1 & 1.5 \\
2.1 & 3.2 & 0.8 \\
1.5 & 0.8 & 2.8
\end{bmatrix}$

\textbf{Part A:} Calculate the eigenvalues and eigenvectors of the covariance matrix.

\textbf{Part B:} Calculate the proportion of variance explained by each principal component.

\textbf{Part C:} If we retain components explaining ≥90\% of variance, how many components do we keep?

\textbf{Part D:} Calculate the principal component scores for a patient with biomarker values $\mathbf{x} = \begin{bmatrix} 2.5 \\ 1.8 \\ 3.2 \end{bmatrix}$.
\end{problem}

\begin{problem}{2.4.3}{Stability Analysis of Drug Interaction Networks}
A drug interaction network has the adjacency matrix:

$\mathbf{A} = \begin{bmatrix}
0 & 0.3 & 0.1 \\
0.2 & 0 & 0.4 \\
0.5 & 0.2 & 0
\end{bmatrix}$

\textbf{Part A:} Calculate the largest eigenvalue (spectral radius) of matrix $\mathbf{A}$.

\textbf{Part B:} Calculate the corresponding eigenvector (Perron vector).

\textbf{Part C:} Interpret the eigenvector components as drug importance scores.

\textbf{Part D:} If the spectral radius > 1, what does this indicate about network stability?
\end{problem}

\subsection{Problems for Section 2.5: Linear Transformations in Drug Data Analysis}

\begin{problem}{2.5.1}{Data Scaling and Normalization}
A pharmaceutical dataset contains the following raw measurements:

$\mathbf{X}_{raw} = \begin{bmatrix}
25 & 180 & 0.8 \\
45 & 220 & 1.2 \\
35 & 160 & 0.6 \\
55 & 200 & 1.0
\end{bmatrix}$

Columns represent: Age (years), Weight (lbs), Creatinine (mg/dL)

\textbf{Part A:} Apply min-max scaling to transform each column to [0,1] range.

\textbf{Part B:} Apply z-score standardization (zero mean, unit variance) to each column.

\textbf{Part C:} Calculate the transformation matrices for both scaling methods.

\textbf{Part D:} Compare the Euclidean distances between patients before and after each transformation.
\end{problem}

\begin{problem}{2.5.2}{Rotation Transformation for Data Visualization}
A 2D scatter plot of drug efficacy vs. toxicity needs rotation for better visualization:

Data points: $\mathbf{P} = \begin{bmatrix}
2 & 1 \\
3 & 4 \\
1 & 2 \\
4 & 3
\end{bmatrix}$

\textbf{Part A:} Create a rotation matrix for 45° counterclockwise rotation.

\textbf{Part B:} Apply the rotation to all data points.

\textbf{Part C:} Calculate the centroid before and after rotation.

\textbf{Part D:} Verify that distances between points are preserved after rotation.
\end{problem}

\begin{problem}{2.5.3}{Projection Transformation for Dimensionality Reduction}
A 3D pharmaceutical dataset needs projection onto a 2D subspace defined by the vectors:

$\mathbf{v}_1 = \begin{bmatrix} 0.6 \\ 0.8 \\ 0.0 \end{bmatrix}$, $\mathbf{v}_2 = \begin{bmatrix} -0.8 \\ 0.6 \\ 0.0 \end{bmatrix}$

Data point: $\mathbf{x} = \begin{bmatrix} 3 \\ 4 \\ 2 \end{bmatrix}$

\textbf{Part A:} Verify that $\mathbf{v}_1$ and $\mathbf{v}_2$ are orthonormal.

\textbf{Part B:} Calculate the projection matrix $\mathbf{P} = \mathbf{v}_1\mathbf{v}_1^T + \mathbf{v}_2\mathbf{v}_2^T$.

\textbf{Part C:} Project the data point onto the 2D subspace.

\textbf{Part D:} Calculate the reconstruction error $\|\mathbf{x} - \mathbf{P}\mathbf{x}\|$.
\end{problem}