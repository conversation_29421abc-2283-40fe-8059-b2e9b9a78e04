% Mathematical Derivations - Enhanced Step-by-Step Explanations
% This file contains detailed derivations for all mathematical equations in the document
% To be integrated into the main document

\section{Enhanced Mathematical Derivations}

This section provides comprehensive step-by-step derivations for all mathematical equations presented in the document, with detailed explanations of the mathematical reasoning behind each step and geometric or physical interpretations where applicable.

\subsection{Therapeutic Index Derivation}

The therapeutic index, introduced by <PERSON> in 1909, quantifies the safety margin of a drug. Let's derive this fundamental concept step by step.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Define the Problem}
We need a mathematical measure that compares a drug's toxic effects to its therapeutic effects. This requires quantifying both toxicity and efficacy.

\textbf{Step 2: Identify Key Dose Points}
From dose-response studies, we identify two critical doses:
\begin{itemize}
\item $TD_{50}$: The dose that produces toxicity in 50\% of subjects
\item $ED_{50}$: The dose that produces the desired therapeutic effect in 50\% of subjects
\end{itemize}

\textbf{Step 3: Establish the Mathematical Relationship}
The therapeutic index is defined as the ratio:
\begin{equation}
\text{Therapeutic Index} = \frac{TD_{50}}{ED_{50}}
\end{equation}

\textbf{Step 4: Mathematical Reasoning}
This ratio provides a dimensionless measure where:
\begin{itemize}
\item TI > 1: The toxic dose is higher than the effective dose (safer drug)
\item TI < 1: The toxic dose is lower than the effective dose (dangerous drug)
\item TI = 1: Toxic and effective doses are equal (very narrow safety margin)
\end{itemize}

\textbf{Step 5: Geometric Interpretation}
On a dose-response plot:
\begin{itemize}
\item The therapeutic index represents the horizontal distance between the efficacy and toxicity curves
\item A larger TI means the curves are further apart (wider safety margin)
\item The logarithm of TI gives the separation on a log-dose scale
\end{itemize}

\textbf{Clinical Significance}: Drugs with TI > 10 are generally considered to have acceptable safety margins, while drugs with TI < 2 require careful monitoring.

\subsection{Michaelis-Menten Kinetics Derivation}

The Michaelis-Menten equation describes enzyme kinetics and is fundamental to understanding drug metabolism. Let's derive it from first principles.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Set Up the Enzyme-Substrate Reaction}
Consider an enzyme E binding to substrate S to form an enzyme-substrate complex ES, which then converts to product P:

\begin{equation}
E + S \xrightleftharpoons[k_{-1}]{k_1} ES \xrightarrow{k_2} E + P
\end{equation}

\textbf{Step 2: Define Rate Constants}
\begin{itemize}
\item $k_1$: Forward rate constant for ES formation
\item $k_{-1}$: Reverse rate constant for ES dissociation
\item $k_2$: Rate constant for product formation
\end{itemize}

\textbf{Step 3: Write Rate Equations}
The rate of ES complex formation:
\begin{equation}
\frac{d[ES]}{dt} = k_1[E][S] - k_{-1}[ES] - k_2[ES]
\end{equation}

\textbf{Step 4: Apply Steady-State Approximation}
Under steady-state conditions, $\frac{d[ES]}{dt} = 0$:
\begin{equation}
k_1[E][S] = k_{-1}[ES] + k_2[ES] = (k_{-1} + k_2)[ES]
\end{equation}

\textbf{Step 5: Solve for [ES]}
\begin{equation}
[ES] = \frac{k_1[E][S]}{k_{-1} + k_2}
\end{equation}

\textbf{Step 6: Define the Michaelis Constant}
The Michaelis constant is defined as:
\begin{equation}
K_m = \frac{k_{-1} + k_2}{k_1}
\end{equation}

Therefore:
\begin{equation}
[ES] = \frac{[E][S]}{K_m}
\end{equation}

\textbf{Step 7: Apply Conservation of Enzyme}
The total enzyme concentration is conserved:
\begin{equation}
[E]_{\text{total}} = [E] + [ES]
\end{equation}

Solving for [E]:
\begin{equation}
[E] = [E]_{\text{total}} - [ES]
\end{equation}

\textbf{Step 8: Substitute Back}
\begin{equation}
[ES] = \frac{([E]_{\text{total}} - [ES])[S]}{K_m}
\end{equation}

\textbf{Step 9: Solve for [ES]}
\begin{equation}
K_m[ES] = ([E]_{\text{total}} - [ES])[S]
\end{equation}
\begin{equation}
K_m[ES] = [E]_{\text{total}}[S] - [ES][S]
\end{equation}
\begin{equation}
K_m[ES] + [ES][S] = [E]_{\text{total}}[S]
\end{equation}
\begin{equation}
[ES](K_m + [S]) = [E]_{\text{total}}[S]
\end{equation}
\begin{equation}
[ES] = \frac{[E]_{\text{total}}[S]}{K_m + [S]}
\end{equation}

\textbf{Step 10: Calculate Reaction Velocity}
The reaction velocity is proportional to [ES]:
\begin{equation}
v = k_2[ES] = k_2 \frac{[E]_{\text{total}}[S]}{K_m + [S]}
\end{equation}

\textbf{Step 11: Define Maximum Velocity}
When all enzyme is bound (saturated), $[ES] = [E]_{\text{total}}$, so:
\begin{equation}
V_{\max} = k_2[E]_{\text{total}}
\end{equation}

\textbf{Step 12: Final Michaelis-Menten Equation}
\begin{equation}
v = \frac{V_{\max}[S]}{K_m + [S]}
\end{equation}

\textbf{Physical Interpretation}:
\begin{itemize}
\item When $[S] \ll K_m$: $v \approx \frac{V_{\max}[S]}{K_m}$ (first-order kinetics)
\item When $[S] \gg K_m$: $v \approx V_{\max}$ (zero-order kinetics)
\item When $[S] = K_m$: $v = \frac{V_{\max}}{2}$ (half-maximal velocity)
\end{itemize}

\subsection{Hill Equation Derivation}

The Hill equation describes cooperative binding and is crucial for understanding dose-response relationships. Let's derive it step by step.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Consider Cooperative Binding}
For a receptor R that binds n drug molecules D cooperatively:
\begin{equation}
R + nD \rightleftharpoons RD_n
\end{equation}

\textbf{Step 2: Write the Equilibrium Expression}
The equilibrium constant is:
\begin{equation}
K_d = \frac{[R][D]^n}{[RD_n]}
\end{equation}

\textbf{Step 3: Define Fractional Occupancy}
The fraction of receptors bound is:
\begin{equation}
\theta = \frac{[RD_n]}{[R]_{\text{total}}} = \frac{[RD_n]}{[R] + [RD_n]}
\end{equation}

\textbf{Step 4: Express [R] in Terms of [RD_n]}
From the equilibrium expression:
\begin{equation}
[R] = \frac{K_d[RD_n]}{[D]^n}
\end{equation}

\textbf{Step 5: Substitute into Fractional Occupancy}
\begin{equation}
\theta = \frac{[RD_n]}{\frac{K_d[RD_n]}{[D]^n} + [RD_n]}
\end{equation}

\textbf{Step 6: Simplify}
\begin{equation}
\theta = \frac{[RD_n]}{[RD_n](\frac{K_d}{[D]^n} + 1)} = \frac{1}{\frac{K_d}{[D]^n} + 1}
\end{equation}

\textbf{Step 7: Multiply Numerator and Denominator by [D]^n}
\begin{equation}
\theta = \frac{[D]^n}{K_d + [D]^n}
\end{equation}

\textbf{Step 8: Relate to Response}
If the response is proportional to occupancy:
\begin{equation}
\text{Response} = E_0 + (E_{\max} - E_0) \times \theta
\end{equation}

\textbf{Step 9: Substitute and Define EC50}
Let $EC_{50}^n = K_d$ (the concentration producing half-maximal response):
\begin{equation}
\text{Response} = E_0 + \frac{(E_{\max} - E_0)[D]^n}{EC_{50}^n + [D]^n}
\end{equation}

\textbf{Step 10: Final Hill Equation}
\begin{equation}
\text{Response} = E_0 + \frac{E_{\max}[D]^n}{EC_{50}^n + [D]^n}
\end{equation}

\textbf{Geometric Interpretation}:
\begin{itemize}
\item $n = 1$: Hyperbolic curve (no cooperativity)
\item $n > 1$: Sigmoidal curve (positive cooperativity)
\item $n < 1$: Less steep curve (negative cooperativity)
\end{itemize}

\subsection{Two-Compartment Model Derivation}

The two-compartment pharmacokinetic model is essential for understanding drug distribution. Let's derive the complete solution step by step.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Set Up the System of Differential Equations}
From mass balance principles:
\begin{equation}
\frac{dA_1}{dt} = k_{21}A_2 - (k_{12} + k_{10})A_1
\end{equation}
\begin{equation}
\frac{dA_2}{dt} = k_{12}A_1 - k_{21}A_2
\end{equation}

\textbf{Step 2: Convert to Matrix Form}
\begin{equation}
\frac{d}{dt}\begin{pmatrix} A_1 \\ A_2 \end{pmatrix} = \begin{pmatrix} -(k_{12}+k_{10}) & k_{21} \\ k_{12} & -k_{21} \end{pmatrix} \begin{pmatrix} A_1 \\ A_2 \end{pmatrix}
\end{equation}

Let $\mathbf{A} = \begin{pmatrix} A_1 \\ A_2 \end{pmatrix}$ and $\mathbf{K} = \begin{pmatrix} -(k_{12}+k_{10}) & k_{21} \\ k_{12} & -k_{21} \end{pmatrix}$

\textbf{Step 3: Write as Matrix Differential Equation}
\begin{equation}
\frac{d\mathbf{A}}{dt} = \mathbf{K}\mathbf{A}
\end{equation}

\textbf{Step 4: Find Eigenvalues of K}
The characteristic equation is:
\begin{equation}
\det(\mathbf{K} - \lambda\mathbf{I}) = 0
\end{equation}

\begin{equation}
\det\begin{pmatrix} -(k_{12}+k_{10})-\lambda & k_{21} \\ k_{12} & -k_{21}-\lambda \end{pmatrix} = 0
\end{equation}

\textbf{Step 5: Expand the Determinant}
\begin{equation}
[-(k_{12}+k_{10})-\lambda][-k_{21}-\lambda] - k_{21}k_{12} = 0
\end{equation}

\begin{equation}
\lambda^2 + \lambda(k_{12}+k_{10}+k_{21}) + k_{10}k_{21} = 0
\end{equation}

\textbf{Step 6: Solve Quadratic Equation}
Let $a = k_{12}+k_{10}+k_{21}$ and $b = k_{10}k_{21}$:
\begin{equation}
\lambda = \frac{-a \pm \sqrt{a^2 - 4b}}{2}
\end{equation}

\textbf{Step 7: Define α and β}
The eigenvalues are $-\alpha$ and $-\beta$ where:
\begin{equation}
\alpha = \frac{a + \sqrt{a^2 - 4b}}{2}
\end{equation}
\begin{equation}
\beta = \frac{a - \sqrt{a^2 - 4b}}{2}
\end{equation}

Note that $\alpha > \beta > 0$ (both positive for stability).

\textbf{Step 8: General Solution Form}
The general solution is:
\begin{equation}
A_1(t) = A_1e^{-\alpha t} + B_1e^{-\beta t}
\end{equation}
\begin{equation}
A_2(t) = A_2e^{-\alpha t} + B_2e^{-\beta t}
\end{equation}

\textbf{Step 9: Apply Initial Conditions}
For IV bolus dose $D$ into central compartment:
\begin{itemize}
\item $A_1(0) = D$
\item $A_2(0) = 0$
\end{itemize}

\textbf{Step 10: Determine Constants}
From initial conditions and the differential equations:
\begin{equation}
A_1 + B_1 = D
\end{equation}
\begin{equation}
A_2 + B_2 = 0
\end{equation}

Using the relationship between compartments:
\begin{equation}
A_1 = \frac{(\alpha - k_{21})D}{\alpha - \beta}
\end{equation}
\begin{equation}
B_1 = \frac{(k_{21} - \beta)D}{\alpha - \beta}
\end{equation}

\textbf{Step 11: Final Solution}
\begin{equation}
A_1(t) = \frac{(\alpha - k_{21})D}{\alpha - \beta}e^{-\alpha t} + \frac{(k_{21} - \beta)D}{\alpha - \beta}e^{-\beta t}
\end{equation}

Converting to concentration by dividing by $V_1$:
\begin{equation}
C_1(t) = Ae^{-\alpha t} + Be^{-\beta t}
\end{equation}

where:
\begin{equation}
A = \frac{(\alpha - k_{21})D}{(\alpha - \beta)V_1}
\end{equation}
\begin{equation}
B = \frac{(k_{21} - \beta)D}{(\alpha - \beta)V_1}
\end{equation}

\textbf{Physical Interpretation}:
\begin{itemize}
\item $\alpha$: Distribution phase rate constant (fast)
\item $\beta$: Elimination phase rate constant (slow)
\item The bi-exponential nature reflects the two processes: distribution and elimination
\end{itemize}

\subsection{Sigmoid Activation Function Derivation}

The sigmoid function is crucial in neural networks and has direct parallels to dose-response relationships in pharmacology.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Define the Requirements}
We need a function that:
\begin{itemize}
\item Maps any real number to the interval (0,1)
\item Is monotonically increasing
\item Has an S-shaped curve
\item Is differentiable everywhere
\end{itemize}

\textbf{Step 2: Start with the Exponential Function}
Consider the exponential function $e^x$:
\begin{itemize}
\item $e^x > 0$ for all $x$
\item $e^x \to 0$ as $x \to -\infty$
\item $e^x \to \infty$ as $x \to \infty$
\end{itemize}

\textbf{Step 3: Create a Bounded Function}
To bound the function between 0 and 1, consider:
\begin{equation}
f(x) = \frac{e^x}{1 + e^x}
\end{equation}

\textbf{Step 4: Verify the Properties}
\textbf{Range}: 
\begin{itemize}
\item As $x \to -\infty$: $e^x \to 0$, so $f(x) \to \frac{0}{1+0} = 0$
\item As $x \to \infty$: $e^x \to \infty$, so $f(x) \to \frac{\infty}{1+\infty} = 1$
\item For any finite $x$: $0 < f(x) < 1$
\end{itemize}

\textbf{Midpoint}:
\begin{equation}
f(0) = \frac{e^0}{1 + e^0} = \frac{1}{1 + 1} = \frac{1}{2}
\end{equation}

\textbf{Step 5: Alternative Form}
Multiply numerator and denominator by $e^{-x}$:
\begin{equation}
f(x) = \frac{e^x \cdot e^{-x}}{(1 + e^x) \cdot e^{-x}} = \frac{1}{e^{-x} + 1}
\end{equation}

This gives us the standard sigmoid function:
\begin{equation}
\sigma(x) = \frac{1}{1 + e^{-x}}
\end{equation}

\textbf{Step 6: Derive the Derivative}
Using the quotient rule:
\begin{equation}
\frac{d\sigma}{dx} = \frac{d}{dx}\left(\frac{1}{1 + e^{-x}}\right)
\end{equation}

Let $u = 1$ and $v = 1 + e^{-x}$:
\begin{equation}
\frac{d\sigma}{dx} = \frac{v \cdot \frac{du}{dx} - u \cdot \frac{dv}{dx}}{v^2}
\end{equation}

Since $\frac{du}{dx} = 0$ and $\frac{dv}{dx} = -e^{-x}$:
\begin{equation}
\frac{d\sigma}{dx} = \frac{0 - 1 \cdot (-e^{-x})}{(1 + e^{-x})^2} = \frac{e^{-x}}{(1 + e^{-x})^2}
\end{equation}

\textbf{Step 7: Simplify the Derivative}
\begin{equation}
\frac{d\sigma}{dx} = \frac{e^{-x}}{(1 + e^{-x})^2} = \frac{1}{1 + e^{-x}} \cdot \frac{e^{-x}}{1 + e^{-x}}
\end{equation}

Since $\frac{e^{-x}}{1 + e^{-x}} = 1 - \frac{1}{1 + e^{-x}} = 1 - \sigma(x)$:
\begin{equation}
\frac{d\sigma}{dx} = \sigma(x)(1 - \sigma(x))
\end{equation}

\textbf{Pharmacological Connection}:
This is identical to the Hill equation with $n=1$, showing the deep connection between neural network activation functions and dose-response relationships in pharmacology.

\subsection{First-Order Differential Equation Solution}

Many pharmacokinetic processes follow first-order kinetics. Let's derive the general solution step by step.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Set Up the Differential Equation}
For first-order elimination:
\begin{equation}
\frac{dA}{dt} = -kA
\end{equation}

where $A$ is the amount of drug and $k$ is the elimination rate constant.

\textbf{Step 2: Separate Variables}
\begin{equation}
\frac{dA}{A} = -k \, dt
\end{equation}

\textbf{Step 3: Integrate Both Sides}
\begin{equation}
\int \frac{dA}{A} = \int -k \, dt
\end{equation}

\textbf{Step 4: Evaluate the Integrals}
\begin{equation}
\ln|A| = -kt + C
\end{equation}

where $C$ is the constant of integration.

\textbf{Step 5: Solve for A}
\begin{equation}
|A| = e^{-kt + C} = e^C \cdot e^{-kt}
\end{equation}

Since $A > 0$ for drug amounts:
\begin{equation}
A = e^C \cdot e^{-kt}
\end{equation}

\textbf{Step 6: Apply Initial Condition}
At $t = 0$, $A = A_0$:
\begin{equation}
A_0 = e^C \cdot e^{0} = e^C
\end{equation}

Therefore, $e^C = A_0$.

\textbf{Step 7: Final Solution}
\begin{equation}
A(t) = A_0 e^{-kt}
\end{equation}

\textbf{Step 8: Convert to Concentration}
If $C = A/V$ where $V$ is volume of distribution:
\begin{equation}
C(t) = C_0 e^{-kt}
\end{equation}

\textbf{Physical Interpretation}:
\begin{itemize}
\item The exponential decay reflects constant fractional elimination
\item Half-life: $t_{1/2} = \frac{\ln(2)}{k}$
\item The rate of elimination is proportional to the amount present
\end{itemize}

\subsection{Matrix-Vector Multiplication Derivation}

Matrix-vector multiplication is fundamental to neural network computations. Let's derive it step by step.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Define the Problem}
We want to multiply matrix $\mathbf{W} \in \mathbb{R}^{m \times n}$ by vector $\mathbf{x} \in \mathbb{R}^n$ to get vector $\mathbf{y} \in \mathbb{R}^m$.

\textbf{Step 2: Write Out the Matrix and Vector}
\begin{equation}
\mathbf{W} = \begin{pmatrix}
w_{11} & w_{12} & \cdots & w_{1n} \\
w_{21} & w_{22} & \cdots & w_{2n} \\
\vdots & \vdots & \ddots & \vdots \\
w_{m1} & w_{m2} & \cdots & w_{mn}
\end{pmatrix}, \quad \mathbf{x} = \begin{pmatrix}
x_1 \\
x_2 \\
\vdots \\
x_n
\end{pmatrix}
\end{equation}

\textbf{Step 3: Define the Result Vector}
\begin{equation}
\mathbf{y} = \mathbf{W}\mathbf{x} = \begin{pmatrix}
y_1 \\
y_2 \\
\vdots \\
y_m
\end{pmatrix}
\end{equation}

\textbf{Step 4: Derive Each Component}
For the $i$-th component of $\mathbf{y}$:
\begin{equation}
y_i = \sum_{j=1}^{n} w_{ij} x_j
\end{equation}

\textbf{Step 5: Expand the First Few Components}
\begin{align}
y_1 &= w_{11}x_1 + w_{12}x_2 + \cdots + w_{1n}x_n \\
y_2 &= w_{21}x_1 + w_{22}x_2 + \cdots + w_{2n}x_n \\
&\vdots \\
y_m &= w_{m1}x_1 + w_{m2}x_2 + \cdots + w_{mn}x_n
\end{align}

\textbf{Step 6: Geometric Interpretation}
Each $y_i$ is the dot product of the $i$-th row of $\mathbf{W}$ with vector $\mathbf{x}$:
\begin{equation}
y_i = \mathbf{w}_i^T \cdot \mathbf{x}
\end{equation}

where $\mathbf{w}_i^T$ is the $i$-th row of $\mathbf{W}$.

\textbf{Step 7: Linear Combination Interpretation}
Alternatively, we can view the result as a linear combination of the columns of $\mathbf{W}$:
\begin{equation}
\mathbf{y} = x_1 \mathbf{w}^{(1)} + x_2 \mathbf{w}^{(2)} + \cdots + x_n \mathbf{w}^{(n)}
\end{equation}

where $\mathbf{w}^{(j)}$ is the $j$-th column of $\mathbf{W}$.

\textbf{Pharmaceutical Application}:
In drug analysis, if $\mathbf{x}$ represents patient characteristics and $\mathbf{W}$ represents drug sensitivity weights, then $\mathbf{y}$ represents predicted drug responses.

\subsection{Softmax Function Derivation}

The softmax function converts a vector of real numbers into a probability distribution, crucial for multi-class classification in pharmaceutical applications.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Define the Requirements}
We need a function that:
\begin{itemize}
\item Takes a vector $\mathbf{x} \in \mathbb{R}^n$
\item Returns a vector $\mathbf{p} \in \mathbb{R}^n$ where $p_i \geq 0$ and $\sum_{i=1}^n p_i = 1$
\item Preserves relative ordering (larger inputs get larger probabilities)
\end{itemize}

\textbf{Step 2: Start with Exponential Function}
To ensure positivity, use the exponential function:
\begin{equation}
\text{unnormalized}_i = e^{x_i}
\end{equation}

\textbf{Step 3: Normalize to Create Probabilities}
To ensure the sum equals 1, divide by the sum of all exponentials:
\begin{equation}
p_i = \frac{e^{x_i}}{\sum_{j=1}^n e^{x_j}}
\end{equation}

\textbf{Step 4: Verify Properties}
\textbf{Non-negativity}: Since $e^{x_i} > 0$ for all $x_i$, we have $p_i > 0$.

\textbf{Normalization}:
\begin{equation}
\sum_{i=1}^n p_i = \sum_{i=1}^n \frac{e^{x_i}}{\sum_{j=1}^n e^{x_j}} = \frac{\sum_{i=1}^n e^{x_i}}{\sum_{j=1}^n e^{x_j}} = 1
\end{equation}

\textbf{Step 5: Numerical Stability Consideration}
For numerical stability, subtract the maximum value:
\begin{equation}
p_i = \frac{e^{x_i - \max_j x_j}}{\sum_{j=1}^n e^{x_j - \max_j x_j}}
\end{equation}

This prevents overflow while preserving the relative values.

\textbf{Step 6: Derive the Derivative}
For the derivative of $p_i$ with respect to $x_j$:

\textbf{Case 1: $i = j$}
\begin{equation}
\frac{\partial p_i}{\partial x_i} = p_i(1 - p_i)
\end{equation}

\textbf{Case 2: $i \neq j$}
\begin{equation}
\frac{\partial p_i}{\partial x_j} = -p_i p_j
\end{equation}

\textbf{Step 7: Matrix Form of Derivative}
The Jacobian matrix is:
\begin{equation}
\mathbf{J}_{ij} = p_i(\delta_{ij} - p_j)
\end{equation}

where $\delta_{ij}$ is the Kronecker delta.

\textbf{Pharmaceutical Application}:
In treatment selection, if $\mathbf{x}$ represents treatment scores, softmax gives the probability of selecting each treatment option.

\subsection{Batch Processing Mathematics}

Batch processing is essential for efficient neural network computation. Let's derive the mathematics step by step.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Define Single Sample Processing}
For one sample $\mathbf{x} \in \mathbb{R}^n$:
\begin{equation}
\mathbf{y} = \mathbf{W}\mathbf{x} + \mathbf{b}
\end{equation}

where $\mathbf{W} \in \mathbb{R}^{m \times n}$ and $\mathbf{b} \in \mathbb{R}^m$.

\textbf{Step 2: Extend to Multiple Samples}
For $B$ samples, stack them into a matrix:
\begin{equation}
\mathbf{X} = \begin{pmatrix}
\mathbf{x}^{(1)T} \\
\mathbf{x}^{(2)T} \\
\vdots \\
\mathbf{x}^{(B)T}
\end{pmatrix} \in \mathbb{R}^{B \times n}
\end{equation}

\textbf{Step 3: Derive Batch Computation}
The batch computation becomes:
\begin{equation}
\mathbf{Y} = \mathbf{X}\mathbf{W}^T + \mathbf{b}^T
\end{equation}

where $\mathbf{Y} \in \mathbb{R}^{B \times m}$.

\textbf{Step 4: Verify Dimensions}
\begin{itemize}
\item $\mathbf{X}$: $B \times n$
\item $\mathbf{W}^T$: $n \times m$
\item $\mathbf{X}\mathbf{W}^T$: $B \times m$
\item $\mathbf{b}^T$: $1 \times m$ (broadcasted to $B \times m$)
\end{itemize}

\textbf{Step 5: Expand the Matrix Multiplication}
\begin{equation}
\mathbf{Y} = \begin{pmatrix}
\mathbf{x}^{(1)T}\mathbf{W}^T \\
\mathbf{x}^{(2)T}\mathbf{W}^T \\
\vdots \\
\mathbf{x}^{(B)T}\mathbf{W}^T
\end{pmatrix} + \begin{pmatrix}
\mathbf{b}^T \\
\mathbf{b}^T \\
\vdots \\
\mathbf{b}^T
\end{pmatrix}
\end{equation}

\textbf{Step 6: Show Equivalence to Individual Processing}
Each row of $\mathbf{Y}$ is:
\begin{equation}
\mathbf{y}^{(i)} = \mathbf{x}^{(i)T}\mathbf{W}^T + \mathbf{b}^T = (\mathbf{W}\mathbf{x}^{(i)} + \mathbf{b})^T
\end{equation}

This is equivalent to processing each sample individually.

\textbf{Step 7: Computational Complexity Analysis}
\textbf{Sequential Processing}: $B \times O(mn) = O(Bmn)$
\textbf{Batch Processing}: $O(Bmn)$ but with better hardware utilization

The advantage comes from vectorized operations and parallel processing capabilities.

\textbf{Pharmaceutical Application}:
Processing multiple patients simultaneously for drug dosing recommendations, where each row represents a patient's characteristics.

\subsection{Eigenvalue and Eigenvector Derivation}

Eigenanalysis is crucial for understanding system behavior in pharmacokinetics and neural networks.

\subsubsection{Step-by-Step Derivation}

\textbf{Step 1: Define the Eigenvalue Problem}
For matrix $\mathbf{A} \in \mathbb{R}^{n \times n}$, we seek scalar $\lambda$ and vector $\mathbf{v} \neq \mathbf{0}$ such that:
\begin{equation}
\mathbf{A}\mathbf{v} = \lambda\mathbf{v}
\end{equation}

\textbf{Step 2: Rearrange the Equation}
\begin{equation}
\mathbf{A}\mathbf{v} - \lambda\mathbf{v} = \mathbf{0}
\end{equation}
\begin{equation}
(\mathbf{A} - \lambda\mathbf{I})\mathbf{v} = \mathbf{0}
\end{equation}

\textbf{Step 3: Condition for Non-trivial Solution}
For a non-trivial solution ($\mathbf{v} \neq \mathbf{0}$), the matrix $(\mathbf{A} - \lambda\mathbf{I})$ must be singular:
\begin{equation}
\det(\mathbf{A} - \lambda\mathbf{I}) = 0
\end{equation}

This is called the characteristic equation.

\textbf{Step 4: Example - 2×2 Matrix}
For $\mathbf{A} = \begin{pmatrix} a & b \\ c & d \end{pmatrix}$:

\begin{equation}
\det\begin{pmatrix} a-\lambda & b \\ c & d-\lambda \end{pmatrix} = 0
\end{equation}

\textbf{Step 5: Expand the Determinant}
\begin{equation}
(a-\lambda)(d-\lambda) - bc = 0
\end{equation}
\begin{equation}
\lambda^2 - (a+d)\lambda + (ad-bc) = 0
\end{equation}

\textbf{Step 6: Solve for Eigenvalues}
Using the quadratic formula:
\begin{equation}
\lambda = \frac{(a+d) \pm \sqrt{(a+d)^2 - 4(ad-bc)}}{2}
\end{equation}

\textbf{Step 7: Find Eigenvectors}
For each eigenvalue $\lambda_i$, solve:
\begin{equation}
(\mathbf{A} - \lambda_i\mathbf{I})\mathbf{v}_i = \mathbf{0}
\end{equation}

\textbf{Step 8: Geometric Interpretation}
\begin{itemize}
\item Eigenvectors are directions that remain unchanged under the transformation $\mathbf{A}$
\item Eigenvalues are the scaling factors along these directions
\item In pharmacokinetics, eigenvalues determine the rates of different phases
\end{itemize}

\textbf{Pharmaceutical Application}:
In compartmental models, eigenvalues determine distribution and elimination rates, while eigenvectors determine the relative amounts in each compartment during each phase.

\subsection{Summary of Mathematical Reasoning}

Each derivation follows a systematic approach:

\begin{enumerate}
\item \textbf{Problem Definition}: Clearly state what we want to derive
\item \textbf{Fundamental Principles}: Start from basic mathematical or physical principles
\item \textbf{Step-by-Step Logic}: Each step follows logically from the previous
\item \textbf{Mathematical Rigor}: Use proper mathematical notation and reasoning
\item \textbf{Verification}: Check results against known properties or limits
\item \textbf{Interpretation}: Provide geometric, physical, or pharmaceutical meaning
\item \textbf{Applications}: Connect to real-world pharmaceutical problems
\end{enumerate}

This systematic approach ensures that clinical pharmacologists can follow the mathematical reasoning and understand both the "how" and "why" behind each equation.