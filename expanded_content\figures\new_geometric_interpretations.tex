% New Geometric Interpretations
% Contains only the NEW geometric figures needed for integration

% Figure: Function Comparison
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Drug Concentration (mg/L)},
        ylabel={Effect (\% of Maximum)},
        xmin=0, xmax=10,
        ymin=0, ymax=110,
        grid=major,
        legend pos=south east,
        title={Function Behavior Comparison in Pharmacology}
    ]
    
    % Linear response
    \addplot[blue, thick, domain=0:10, samples=100] {10*x};
    \addlegendentry{Linear Response}
    
    % <PERSON><PERSON>-<PERSON><PERSON> (hyperbolic)
    \addplot[red, thick, domain=0:10, samples=100] {100*x/(2+x)};
    \addlegendentry{<PERSON>is-<PERSON>ten}
    
    % Sigmoid (Hill equation)
    \addplot[green, thick, domain=0:10, samples=100] {100*x^2/(3^2+x^2)};
    \addlegendentry{Sigmoid (Hill)}
    
    % Logarithmic
    \addplot[purple, thick, domain=0.1:10, samples=100] {30*ln(x+1)};
    \addlegendentry{Logarithmic}
    
    % EC50 markers
    \draw[dashed, gray] (axis cs:2,0) -- (axis cs:2,50);
    \draw[dashed, gray] (axis cs:3,0) -- (axis cs:3,50);
    \node[gray] at (axis cs:2.5,15) {$EC_{50}$};
    
    % Annotations
    \node[blue] at (axis cs:7,80) {Constant slope};
    \node[red] at (axis cs:6,70) {Saturation kinetics};
    \node[green] at (axis cs:8,60) {Cooperative binding};
    \node[purple] at (axis cs:8,40) {Diminishing returns};
    
    \end{axis}
\end{tikzpicture}
\caption{Function Behavior Comparison: Linear relationships show constant proportionality between dose and effect. Michaelis-Menten kinetics demonstrate saturation at high concentrations. Sigmoid relationships show threshold effects and cooperativity. Logarithmic functions exhibit diminishing returns. The $EC_{50}$ represents the concentration producing 50\% of maximum effect.}
\label{fig:function_comparison}
\end{figure}

% Figure: PK Models Comparison
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Time (hours)},
        ylabel={Drug Concentration (mg/L)},
        xmin=0, xmax=12,
        ymin=0, ymax=12,
        grid=major,
        legend pos=north east,
        title={Pharmacokinetic Model Comparison}
    ]
    
    % One-compartment model
    \addplot[blue, thick, domain=0:12, samples=100] {10*exp(-0.3*x)};
    \addlegendentry{One-Compartment}
    
    % Two-compartment model (bi-exponential)
    \addplot[red, thick, domain=0:12, samples=100] {7*exp(-1.5*x) + 3*exp(-0.1*x)};
    \addlegendentry{Two-Compartment}
    
    % Three-compartment model
    \addplot[green, thick, domain=0:12, samples=100] {5*exp(-3*x) + 3*exp(-0.5*x) + 2*exp(-0.05*x)};
    \addlegendentry{Three-Compartment}
    
    % Annotations
    \node[blue] at (axis cs:2,6) {Simple exponential decay};
    \node[red] at (axis cs:4,4) {Distribution + elimination phases};
    \node[green] at (axis cs:6,2.5) {Complex multi-phase kinetics};
    
    \end{axis}
\end{tikzpicture}
\caption{Pharmacokinetic Model Comparison: Different compartmental models capture varying levels of physiological complexity. One-compartment models show simple exponential decay, two-compartment models capture distribution and elimination phases, while three-compartment models describe complex tissue distribution patterns. Model selection depends on drug properties and required precision.}
\label{fig:pk_models}
\end{figure}

% Figure: Dose Adjustment Algorithm
\begin{figure}[H]
\centering
\begin{tikzpicture}[
    node distance=1.5cm,
    decision/.style={diamond, draw, fill=blue!20, text width=4em, text badly centered, inner sep=0pt},
    process/.style={rectangle, draw, fill=green!20, text width=6em, text centered, minimum height=2em},
    terminal/.style={ellipse, draw, fill=red!20, text centered, minimum height=2em}
]

% Start
\node[terminal] (start) {Start};

% Initial dose
\node[process, below of=start] (initial) {Calculate Initial Dose};

% Measure concentration
\node[process, below of=initial] (measure) {Measure Drug Concentration};

% Decision: In range?
\node[decision, below of=measure] (inrange) {In Therapeutic Range?};

% Adjust dose
\node[process, left of=inrange, xshift=-3cm] (adjust) {Adjust Dose};

% Monitor
\node[process, right of=inrange, xshift=3cm] (monitor) {Continue Monitoring};

% End
\node[terminal, below of=inrange, yshift=-1cm] (end) {End};

% Arrows
\draw[->] (start) -- (initial);
\draw[->] (initial) -- (measure);
\draw[->] (measure) -- (inrange);
\draw[->] (inrange) -- node[above] {No} (adjust);
\draw[->] (inrange) -- node[above] {Yes} (monitor);
\draw[->] (adjust) |- (measure);
\draw[->] (monitor) |- (end);

% Algorithm details
\node[below, text width=12cm] at (0,-6) {
    \textbf{Dose Adjustment Algorithm:}
    \begin{enumerate}
        \item Calculate initial dose based on patient characteristics
        \item Measure steady-state concentration
        \item Compare to therapeutic range (MEC-MTC)
        \item If outside range, adjust dose proportionally
        \item Repeat until therapeutic target achieved
    \end{enumerate}
};

\end{tikzpicture}
\caption{Dose Adjustment Algorithm Flowchart: A systematic approach to therapeutic drug monitoring and dose optimization. The algorithm iteratively adjusts dosing based on measured concentrations, demonstrating how clinical decision-making can be formalized into computational processes similar to neural network training loops.}
\label{fig:dose_adjustment_algorithm}
\end{figure}

% Figure: Neural Network Architecture (Basic)
\begin{figure}[H]
\centering
\begin{tikzpicture}[
    neuron/.style={circle,draw,minimum size=1cm,inner sep=0pt},
    input/.style={neuron,fill=blue!20},
    hidden/.style={neuron,fill=green!20},
    output/.style={neuron,fill=red!20}
]

% Input layer
\foreach \i in {1,2,3,4} {
    \node[input] (i\i) at (0,\i-2.5) {};
    \node[left] at (-0.7,\i-2.5) {$x_\i$};
}
\node[below] at (0,-3.2) {Input Layer};

% Hidden layer 1
\foreach \i in {1,2,3} {
    \node[hidden] (h1\i) at (3,\i-2) {};
}
\node[below] at (3,-2.7) {Hidden Layer 1};

% Hidden layer 2
\foreach \i in {1,2,3} {
    \node[hidden] (h2\i) at (6,\i-2) {};
}
\node[below] at (6,-2.7) {Hidden Layer 2};

% Output layer
\foreach \i in {1,2} {
    \node[output] (o\i) at (9,\i-1.5) {};
    \node[right] at (9.7,\i-1.5) {$y_\i$};
}
\node[below] at (9,-2.2) {Output Layer};

% Connections (sample)
\foreach \i in {1,2,3,4} {
    \foreach \j in {1,2,3} {
        \draw[->] (i\i) -- (h1\j);
    }
}

\foreach \i in {1,2,3} {
    \foreach \j in {1,2,3} {
        \draw[->] (h1\i) -- (h2\j);
    }
}

\foreach \i in {1,2,3} {
    \foreach \j in {1,2} {
        \draw[->] (h2\i) -- (o\j);
    }
}

% Labels
\node[above] at (0,2.2) {Patient Data};
\node[above] at (3,1.7) {Feature Extraction};
\node[above] at (6,1.7) {Pattern Recognition};
\node[above] at (9,1.2) {Predictions};

% Activation function inset
\begin{scope}[shift={(11,0)}, scale=0.5]
    \draw[->] (-1,0) -- (1,0) node[right] {$z$};
    \draw[->] (0,-0.5) -- (0,1.5) node[above] {$\sigma(z)$};
    \draw[thick,blue] plot[domain=-3:3,samples=100] (\x/3,{1/(1+exp(-3*\x))});
    \node[below] at (0,-1) {Sigmoid: $\sigma(z) = \frac{1}{1+e^{-z}}$};
\end{scope}

\end{tikzpicture}
\caption{Basic Neural Network Architecture: A feedforward network processes patient data through multiple layers. Input neurons receive patient characteristics, hidden layers extract increasingly complex features, and output neurons generate predictions. The sigmoid activation function (inset) introduces non-linearity essential for learning complex patterns in pharmaceutical data.}
\label{fig:neural_network_architecture}
\end{figure}