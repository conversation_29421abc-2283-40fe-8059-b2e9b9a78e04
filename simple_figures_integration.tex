% Simple Figure Integration for Neural Networks Document
% This file contains essential figures without package redeclarations

% Vector Addition Figure
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.2]
    % Coordinate system
    \draw[->] (-0.5,0) -- (4,0) node[right] {Age (years)};
    \draw[->] (0,-0.5) -- (0,3.5) node[above] {Weight (kg)};
    
    % Vector A (patient characteristics)
    \draw[->,thick,blue] (0,0) -- (3,1) node[midway,above left] {$\vec{A}$};
    \draw[dashed,blue] (3,0) -- (3,1);
    \draw[dashed,blue] (0,1) -- (3,1);
    
    % Vector B (treatment parameters)
    \draw[->,thick,red] (0,0) -- (1,2.5) node[midway,left] {$\vec{B}$};
    \draw[dashed,red] (1,0) -- (1,2.5);
    \draw[dashed,red] (0,2.5) -- (1,2.5);
    
    % Vector sum A + B
    \draw[->,thick,green] (0,0) -- (4,3.5) node[midway,above right] {$\vec{A} + \vec{B}$};
    
    % Parallelogram construction
    \draw[dotted,gray] (3,1) -- (4,3.5);
    \draw[dotted,gray] (1,2.5) -- (4,3.5);
    
    % Labels
    \node[below] at (1.5,0) {Patient: 65 years};
    \node[left] at (0,0.5) {70 kg};
    \node[below] at (0.5,0) {Dose: 10 mg};
    \node[left] at (0,1.25) {Frequency: 2/day};
\end{tikzpicture}
\caption{Vector Addition in Pharmaceutical Context: Patient characteristics (vector A) and treatment parameters (vector B) combine to determine therapeutic outcome (vector A + B). This geometric representation shows how multiple factors contribute to the overall treatment effect.}
\label{fig:vector_addition}
\end{figure}

% Pharmacokinetic Profiles Figure
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=12cm,
        height=8cm,
        xlabel={Time (hours)},
        ylabel={Concentration (mg/L)},
        xmin=0, xmax=24,
        ymin=0, ymax=12,
        grid=major,
        legend pos=north east,
        title={Pharmacokinetic Profiles: Different Dosing Regimens}
    ]
    
    % Single IV bolus
    \addplot[blue,thick,domain=0:24,samples=100] {10*exp(-0.2*x)};
    \addlegendentry{Single IV Bolus};
    
    % Multiple dosing approximation
    \addplot[red,thick,samples=50] coordinates {
        (0,8) (1,6.5) (2,5.3) (3,4.3) (4,3.5) (5,2.9) (6,10.4)
        (7,8.5) (8,6.9) (9,5.6) (10,4.6) (11,3.7) (12,11.0)
        (13,9.0) (14,7.3) (15,5.9) (16,4.8) (17,3.9) (18,11.4)
        (19,9.3) (20,7.6) (21,6.2) (22,5.0) (23,4.1) (24,3.3)
    };
    \addlegendentry{Multiple Dosing};
    
    % Therapeutic window
    \fill[gray,opacity=0.2] (axis cs:0,2) rectangle (axis cs:24,6);
    \draw[dashed,gray] (axis cs:0,2) -- (axis cs:24,2);
    \draw[dashed,gray] (axis cs:0,6) -- (axis cs:24,6);
    \node[gray] at (axis cs:20,4) {Therapeutic Window};
    
    \end{axis}
\end{tikzpicture}
\caption{Pharmacokinetic Profiles: Single bolus dosing shows exponential decay, while multiple dosing creates accumulation patterns. The therapeutic window (gray area) represents the target concentration range for optimal efficacy and safety.}
\label{fig:pk_profiles}
\end{figure}

% Matrix Multiplication Visualization
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.8]
    % Matrix A
    \draw[thick] (0,0) rectangle (2,3);
    \node at (1,2.5) {$a_{11}$};
    \node at (1,1.5) {$a_{21}$};
    \node at (1,0.5) {$a_{31}$};
    \node at (0.5,2.5) {$a_{12}$};
    \node at (0.5,1.5) {$a_{22}$};
    \node at (0.5,0.5) {$a_{32}$};
    \node[below] at (1,-0.3) {Matrix A (3×2)};
    
    % Matrix B
    \draw[thick] (3,1) rectangle (5,3);
    \node at (3.5,2.5) {$b_{11}$};
    \node at (4.5,2.5) {$b_{12}$};
    \node at (3.5,1.5) {$b_{21}$};
    \node at (4.5,1.5) {$b_{22}$};
    \node[below] at (4,0.7) {Matrix B (2×2)};
    
    % Multiplication symbol
    \node at (2.5,1.5) {$\times$};
    
    % Result Matrix C
    \draw[thick] (6,1) rectangle (8,3);
    \node at (6.5,2.5) {$c_{11}$};
    \node at (7.5,2.5) {$c_{12}$};
    \node at (6.5,1.5) {$c_{21}$};
    \node at (7.5,1.5) {$c_{22}$};
    \node at (6.5,0.5) {$c_{31}$};
    \node at (7.5,0.5) {$c_{32}$};
    \node[below] at (7,0.2) {Matrix C (3×2)};
    
    % Equals symbol
    \node at (5.5,1.5) {$=$};
    
    % Highlight calculation
    \draw[red,thick] (0,2.3) rectangle (2,2.7);
    \draw[red,thick] (3,2.3) rectangle (3.7,2.7);
    \draw[red,thick] (6,2.3) rectangle (6.7,2.7);
    
    % Calculation annotation
    \node[red,below] at (4,-1) {$c_{11} = a_{11} \cdot b_{11} + a_{12} \cdot b_{21}$};
\end{tikzpicture}
\caption{Matrix Multiplication Visualization: The element $c_{11}$ (highlighted in red) is computed as the dot product of the first row of matrix A with the first column of matrix B. This operation is fundamental to neural network computations.}
\label{fig:matrix_multiplication}
\end{figure}

% Dose-Response Curves
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=12cm,
        height=8cm,
        xlabel={Drug Concentration (mg/L)},
        ylabel={Effect (\% of Maximum)},
        xmin=0, xmax=20,
        ymin=0, ymax=110,
        grid=major,
        legend pos=south east,
        title={Dose-Response Relationships in Pharmacology}
    ]
    
    % Linear response
    \addplot[blue,thick,domain=0:20,samples=50] {5*x};
    \addlegendentry{Linear Response};
    
    % Sigmoid (Hill) response
    \addplot[red,thick,domain=0:20,samples=100] {100*x^2/(25+x^2)};
    \addlegendentry{Sigmoid (Hill) Response};
    
    % Logarithmic response
    \addplot[green,thick,domain=0.1:20,samples=100] {30*ln(x+1)};
    \addlegendentry{Logarithmic Response};
    
    % EC50 line for sigmoid
    \draw[dashed,red] (axis cs:5,0) -- (axis cs:5,50);
    \draw[dashed,red] (axis cs:0,50) -- (axis cs:5,50);
    \node[red] at (axis cs:6,25) {$EC_{50}$};
    
    \end{axis}
\end{tikzpicture}
\caption{Dose-Response Relationships: Different mathematical models describe how drug effects vary with concentration. Linear responses show proportional relationships, sigmoid curves capture saturation effects (common in receptor binding), and logarithmic responses represent diminishing returns. The $EC_{50}$ represents the concentration producing 50\% of maximum effect.}
\label{fig:dose_response}
\end{figure}

% Neural Network Architecture
\begin{figure}[H]
\centering
\begin{tikzpicture}[
    neuron/.style={circle,draw,minimum size=1cm,inner sep=0pt},
    input/.style={neuron,fill=blue!20},
    hidden/.style={neuron,fill=green!20},
    output/.style={neuron,fill=red!20}
]
    % Input layer
    \node[input] (i1) at (0,3) {$x_1$};
    \node[input] (i2) at (0,2) {$x_2$};
    \node[input] (i3) at (0,1) {$x_3$};
    \node[input] (i4) at (0,0) {$x_4$};
    \node[below] at (0,-0.5) {Input Layer};
    \node[below] at (0,-0.8) {(Patient Data)};
    
    % Hidden layer
    \node[hidden] (h1) at (3,2.5) {$h_1$};
    \node[hidden] (h2) at (3,1.5) {$h_2$};
    \node[hidden] (h3) at (3,0.5) {$h_3$};
    \node[below] at (3,-0.5) {Hidden Layer};
    \node[below] at (3,-0.8) {(Feature Extraction)};
    
    % Output layer
    \node[output] (o1) at (6,1.5) {$y$};
    \node[below] at (6,0.5) {Output Layer};
    \node[below] at (6,0.2) {(Drug Response)};
    
    % Connections
    \foreach \i in {1,2,3,4}
        \foreach \h in {1,2,3}
            \draw[->] (i\i) -- (h\h);
    
    \foreach \h in {1,2,3}
        \draw[->] (h\h) -- (o1);
    
    % Weight annotations
    \node at (1.5,2.8) {$W_1$};
    \node at (4.5,2) {$W_2$};
    
    % Activation function
    \node[above] at (3,3.2) {$\sigma(W_1 x + b_1)$};
    \node[above] at (6,2.2) {$\sigma(W_2 h + b_2)$};
\end{tikzpicture}
\caption{Neural Network Architecture for Drug Response Prediction: Patient characteristics (age, weight, genetics, comorbidities) form the input layer. Hidden layers extract relevant features through weighted combinations and non-linear activation functions. The output predicts drug response. This architecture mirrors the multi-step biological processes in pharmacokinetics and pharmacodynamics.}
\label{fig:neural_network}
\end{figure}

% Eigenvalue Visualization
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.5]
    % Coordinate system
    \draw[->] (-2,0) -- (3,0) node[right] {$x$};
    \draw[->] (0,-2) -- (0,3) node[above] {$y$};
    
    % Original vectors
    \draw[->,thick,blue] (0,0) -- (1,0) node[below right] {$\vec{v_1}$};
    \draw[->,thick,blue] (0,0) -- (0,1) node[above left] {$\vec{v_2}$};
    \draw[->,thick,red] (0,0) -- (1,1) node[above right] {$\vec{u}$};
    
    % Transformed vectors
    \draw[->,thick,blue,dashed] (0,0) -- (2,0) node[below right] {$A\vec{v_1} = 2\vec{v_1}$};
    \draw[->,thick,blue,dashed] (0,0) -- (0,0.5) node[above left] {$A\vec{v_2} = 0.5\vec{v_2}$};
    \draw[->,thick,red,dashed] (0,0) -- (1.5,1.5) node[above right] {$A\vec{u}$};
    
    % Eigenvalue annotations
    \node[blue] at (1,-0.5) {$\lambda_1 = 2$};
    \node[blue] at (-1.5,1.5) {$\lambda_2 = 0.5$};
    
    % Matrix representation
    \node[below] at (0,-2.5) {$A = \begin{bmatrix} 2 & 0 \\ 0 & 0.5 \end{bmatrix}$};
\end{tikzpicture}
\caption{Eigenvalue and Eigenvector Visualization: Eigenvectors (blue) maintain their direction under linear transformation, scaling only by their eigenvalues. Non-eigenvectors (red) change both magnitude and direction. In pharmacology, eigenvectors represent principal modes of drug action, while eigenvalues indicate the strength of each mode.}
\label{fig:eigenvalues}
\end{figure}