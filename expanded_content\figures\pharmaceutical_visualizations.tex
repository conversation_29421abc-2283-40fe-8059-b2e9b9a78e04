% Pharmaceutical Data Visualizations
% Specialized plots and diagrams for pharmacological concepts

\usepackage{tikz}
\usepackage{pgfplots}
\pgfplotsset{compat=1.18}
\usetikzlibrary{arrows.meta,positioning,shapes.geometric,calc,decorations.pathreplacing,patterns,3d}

% Pharmacokinetic and Pharmacodynamic Relationships

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Time (hours)},
        ylabel={Concentration (mg/L)},
        xmin=0, xmax=24,
        ymin=0, ymax=12,
        grid=major,
        legend pos=north east,
        title={Pharmacokinetic Profiles: Multiple Dosing Regimens}
    ]
    
    % Single IV bolus
    \addplot[blue,thick,domain=0:24,samples=100] {10*exp(-0.2*x)};
    \addlegendentry{Single IV Bolus (10 mg)};
    
    % Multiple dosing (every 6 hours)
    \addplot[red,thick,domain=0:6,samples=50] {8*exp(-0.2*x)};
    \addplot[red,thick,domain=6:12,samples=50] {8*exp(-0.2*x) + 8*exp(-0.2*(x-6))};
    \addplot[red,thick,domain=12:18,samples=50] {8*exp(-0.2*x) + 8*exp(-0.2*(x-6)) + 8*exp(-0.2*(x-12))};
    \addplot[red,thick,domain=18:24,samples=50] {8*exp(-0.2*x) + 8*exp(-0.2*(x-6)) + 8*exp(-0.2*(x-12)) + 8*exp(-0.2*(x-18))};
    \addlegendentry{Multiple Dosing (8 mg q6h)};
    
    % Continuous infusion
    \addplot[green,thick,domain=0:24,samples=100] {5*(1-exp(-0.2*x))};
    \addlegendentry{Continuous Infusion (1 mg/h)};
    
    % Therapeutic window
    \fill[gray,opacity=0.2] (axis cs:0,2) rectangle (axis cs:24,6);
    \draw[dashed,gray] (axis cs:0,2) -- (axis cs:24,2);
    \draw[dashed,gray] (axis cs:0,6) -- (axis cs:24,6);
    \node[gray] at (axis cs:20,4) {Therapeutic Window};
    \node[gray] at (axis cs:22,2.3) {MEC};
    \node[gray] at (axis cs:22,5.7) {MTC};
    
    % Dosing arrows
    \foreach \x in {0,6,12,18} {
        \draw[->,red,thick] (axis cs:\x,11) -- (axis cs:\x,10);
    }
    \node[red] at (axis cs:9,11.5) {Doses};
    
    \end{axis}
\end{tikzpicture}
\caption{Pharmacokinetic Profiles Comparison: Different dosing regimens produce distinct concentration-time profiles. Single bolus shows exponential decay, multiple dosing creates saw-tooth patterns with accumulation, and continuous infusion approaches steady-state asymptotically. The therapeutic window (gray area) shows the target concentration range between minimum effective concentration (MEC) and minimum toxic concentration (MTC).}
\label{fig:pk_profiles}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Drug Concentration (mg/L)},
        ylabel={Effect (\% of Maximum)},
        xmin=0.1, xmax=100,
        ymin=0, ymax=110,
        xmode=log,
        grid=major,
        legend pos=south east,
        title={Dose-Response Relationships: Different Receptor Models}
    ]
    
    % Simple Emax model
    \addplot[blue,thick,domain=0.1:100,samples=100] {100*x/(5+x)};
    \addlegendentry{Simple Emax: $E = \frac{100C}{5+C}$};
    
    % Sigmoid Emax (Hill equation, n=2)
    \addplot[red,thick,domain=0.1:100,samples=100] {100*x^2/(10^2+x^2)};
    \addlegendentry{Sigmoid (n=2): $E = \frac{100C^2}{10^2+C^2}$};
    
    % Sigmoid Emax (Hill equation, n=4)
    \addplot[green,thick,domain=0.1:100,samples=100] {100*x^4/(15^4+x^4)};
    \addlegendentry{Steep Sigmoid (n=4): $E = \frac{100C^4}{15^4+C^4}$};
    
    % Inhibitory Emax
    \addplot[purple,thick,domain=0.1:100,samples=100] {100 - 80*x/(8+x)};
    \addlegendentry{Inhibitory: $E = 100 - \frac{80C}{8+C}$};
    
    % EC50 markers
    \draw[dashed,blue] (axis cs:5,0) -- (axis cs:5,50);
    \draw[dashed,blue] (axis cs:0.1,50) -- (axis cs:5,50);
    \node[blue] at (axis cs:6,25) {$EC_{50}=5$};
    
    \draw[dashed,red] (axis cs:10,0) -- (axis cs:10,50);
    \draw[dashed,red] (axis cs:0.1,50) -- (axis cs:10,50);
    \node[red] at (axis cs:12,25) {$EC_{50}=10$};
    
    \draw[dashed,green] (axis cs:15,0) -- (axis cs:15,50);
    \draw[dashed,green] (axis cs:0.1,50) -- (axis cs:15,50);
    \node[green] at (axis cs:18,25) {$EC_{50}=15$};
    
    \end{axis}
\end{tikzpicture}
\caption{Pharmacodynamic Dose-Response Models: Different mathematical models describe various receptor-drug interactions. Simple Emax shows hyperbolic response, sigmoid models show cooperative binding (steeper curves with higher Hill coefficients), and inhibitory models show decreasing effects. These curves mirror activation functions used in neural networks.}
\label{fig:dose_response_models}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Time (hours)},
        ylabel left={Concentration (mg/L)},
        ylabel right={Effect (\%)},
        xmin=0, xmax=24,
        ymin=0, ymax=10,
        y2min=0, y2max=100,
        grid=major,
        legend pos=north east,
        title={PK-PD Relationship: Concentration vs. Effect Over Time},
        axis y line*=left,
        axis y line*=right
    ]
    
    % Concentration-time profile
    \addplot[blue,thick,domain=0:24,samples=100] {8*exp(-0.15*x)};
    \addlegendentry{Plasma Concentration};
    
    % Direct effect (no delay)
    \addplot[red,thick,domain=0:24,samples=100,axis y line*=right] {100*(8*exp(-0.15*x))/(3+(8*exp(-0.15*x)))};
    \addlegendentry{Direct Effect};
    
    % Delayed effect (effect compartment)
    \addplot[green,thick,domain=0:24,samples=100,axis y line*=right] {100*(8*0.5*(exp(-0.15*x)-exp(-0.5*x)))/(3+(8*0.5*(exp(-0.15*x)-exp(-0.5*x))))};
    \addlegendentry{Delayed Effect};
    
    % Tolerance effect (decreasing over time)
    \addplot[purple,thick,domain=0:24,samples=100,axis y line*=right] {100*(8*exp(-0.15*x))/(3+(8*exp(-0.15*x)))*exp(-0.05*x)};
    \addlegendentry{Effect with Tolerance};
    
    \end{axis}
    
    % Add second y-axis
    \begin{axis}[
        width=14cm,
        height=10cm,
        xmin=0, xmax=24,
        y2min=0, y2max=100,
        axis y line*=right,
        axis x line=none,
        ylabel near ticks,
        ylabel={Effect (\%)},
        hide x axis
    ]
    \end{axis}
\end{tikzpicture}
\caption{Pharmacokinetic-Pharmacodynamic (PK-PD) Relationships: The relationship between drug concentration and effect can be direct (immediate), delayed (due to distribution to effect site), or modified by tolerance. These temporal relationships are crucial for understanding drug action and parallel the temporal dynamics in recurrent neural networks.}
\label{fig:pkpd_relationships}
\end{figure}

% High-Dimensional Pharmaceutical Data Visualizations

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % Patient similarity heatmap
    \begin{axis}[
        width=12cm,
        height=10cm,
        xlabel={Patient ID},
        ylabel={Patient ID},
        xmin=0.5, xmax=10.5,
        ymin=0.5, ymax=10.5,
        xtick={1,2,3,4,5,6,7,8,9,10},
        ytick={1,2,3,4,5,6,7,8,9,10},
        colorbar,
        colormap/hot,
        title={Patient Similarity Matrix: Multi-dimensional Clinical Data}
    ]
    
    % Similarity matrix data (symmetric)
    \addplot[matrix plot*,point meta=explicit] coordinates {
        (1,1) [1.0] (1,2) [0.8] (1,3) [0.3] (1,4) [0.2] (1,5) [0.1] (1,6) [0.4] (1,7) [0.6] (1,8) [0.2] (1,9) [0.3] (1,10) [0.5]
        (2,1) [0.8] (2,2) [1.0] (2,3) [0.4] (2,4) [0.3] (2,5) [0.2] (2,6) [0.5] (2,7) [0.7] (2,8) [0.3] (2,9) [0.4] (2,10) [0.6]
        (3,1) [0.3] (3,2) [0.4] (3,3) [1.0] (3,4) [0.9] (3,5) [0.8] (3,6) [0.2] (3,7) [0.1] (3,8) [0.7] (3,9) [0.8] (3,10) [0.2]
        (4,1) [0.2] (4,2) [0.3] (4,3) [0.9] (4,4) [1.0] (4,5) [0.9] (4,6) [0.1] (4,7) [0.2] (4,8) [0.8] (4,9) [0.9] (4,10) [0.1]
        (5,1) [0.1] (5,2) [0.2] (5,3) [0.8] (5,4) [0.9] (5,5) [1.0] (5,6) [0.1] (5,7) [0.1] (5,8) [0.9] (5,9) [0.8] (5,10) [0.1]
        (6,1) [0.4] (6,2) [0.5] (6,3) [0.2] (6,4) [0.1] (6,5) [0.1] (6,6) [1.0] (6,7) [0.8] (6,8) [0.2] (6,9) [0.1] (6,10) [0.9]
        (7,1) [0.6] (7,2) [0.7] (7,3) [0.1] (7,4) [0.2] (7,5) [0.1] (7,6) [0.8] (7,7) [1.0] (7,8) [0.1] (7,9) [0.2] (7,10) [0.8]
        (8,1) [0.2] (8,2) [0.3] (8,3) [0.7] (8,4) [0.8] (8,5) [0.9] (8,6) [0.2] (8,7) [0.1] (8,8) [1.0] (8,9) [0.7] (8,10) [0.2]
        (9,1) [0.3] (9,2) [0.4] (9,3) [0.8] (9,4) [0.9] (9,5) [0.8] (9,6) [0.1] (9,7) [0.2] (9,8) [0.7] (9,9) [1.0] (9,10) [0.1]
        (10,1) [0.5] (10,2) [0.6] (10,3) [0.2] (10,4) [0.1] (10,5) [0.1] (10,6) [0.9] (10,7) [0.8] (10,8) [0.2] (10,9) [0.1] (10,10) [1.0]
    };
    
    \end{axis}
    
    % Cluster annotations
    \draw[thick,red] (1.5,8.5) rectangle (3.5,6.5);
    \node[red] at (4,7.5) {Cluster 1};
    
    \draw[thick,blue] (4.5,5.5) rectangle (7.5,2.5);
    \node[blue] at (8,4) {Cluster 2};
    
    \draw[thick,green] (8.5,9.5) rectangle (10.5,7.5);
    \node[green] at (9.5,6.5) {Cluster 3};
    
    % Clinical interpretation
    \node[below,text width=12cm,align=center] at (6,-1.5) {
        \textbf{Clinical Interpretation:} This heatmap shows patient similarity based on multiple clinical variables (age, weight, lab values, comorbidities). Bright colors indicate high similarity. Clustering reveals patient subgroups that may respond similarly to treatments, enabling personalized medicine approaches.
    };
\end{tikzpicture}
\caption{High-Dimensional Patient Similarity Visualization: A heatmap representation of patient similarity calculated from multiple clinical variables. Patients with similar characteristics cluster together (red boxes), revealing natural groupings that can inform treatment decisions. This type of analysis is fundamental to neural network-based patient stratification.}
\label{fig:patient_similarity}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Principal Component 1 (45\% variance)},
        ylabel={Principal Component 2 (23\% variance)},
        xmin=-4, xmax=4,
        ymin=-3, ymax=3,
        grid=major,
        legend pos=north west,
        title={Drug Response Data: PCA Visualization of Patient Clusters}
    ]
    
    % Cluster 1: Young, healthy patients
    \addplot[only marks,mark=*,mark size=3pt,blue] coordinates {
        (-2.5,1.5) (-2.2,1.8) (-2.8,1.2) (-2.1,1.6) (-2.6,1.4) (-2.3,1.9) (-2.7,1.1) (-2.4,1.7)
    };
    \addlegendentry{Young, Healthy (n=8)};
    
    % Cluster 2: Elderly patients
    \addplot[only marks,mark=square*,mark size=3pt,red] coordinates {
        (2.1,-1.8) (2.4,-1.5) (1.8,-2.1) (2.2,-1.6) (2.5,-1.4) (1.9,-1.9) (2.3,-1.7) (2.0,-2.0)
    };
    \addlegendentry{Elderly (n=8)};
    
    % Cluster 3: Patients with comorbidities
    \addplot[only marks,mark=triangle*,mark size=4pt,green] coordinates {
        (0.2,2.1) (0.5,1.8) (-0.1,2.3) (0.3,1.9) (0.1,2.2) (0.4,2.0) (-0.2,2.4) (0.6,1.7)
    };
    \addlegendentry{Comorbidities (n=8)};
    
    % Cluster 4: Renal impairment
    \addplot[only marks,mark=diamond*,mark size=4pt,purple] coordinates {
        (-0.8,-1.2) (-1.1,-0.9) (-0.5,-1.5) (-0.9,-1.0) (-1.2,-0.8) (-0.6,-1.4) (-1.0,-1.1) (-0.7,-1.3)
    };
    \addlegendentry{Renal Impairment (n=8)};
    
    % Cluster boundaries (ellipses)
    \draw[blue,thick,dashed] (-2.5,1.5) ellipse (0.4 and 0.4);
    \draw[red,thick,dashed] (2.1,-1.8) ellipse (0.4 and 0.4);
    \draw[green,thick,dashed] (0.2,2.1) ellipse (0.4 and 0.4);
    \draw[purple,thick,dashed] (-0.8,-1.2) ellipse (0.4 and 0.4);
    
    % Loading vectors (showing which original variables contribute to PCs)
    \draw[->,thick,orange] (0,0) -- (2.5,1) node[above right] {Age};
    \draw[->,thick,orange] (0,0) -- (-1.5,2) node[above left] {Creatinine};
    \draw[->,thick,orange] (0,0) -- (1,-2) node[below right] {Weight};
    \draw[->,thick,orange] (0,0) -- (-2,-1) node[below left] {Albumin};
    
    \end{axis}
    
    % Clinical interpretation
    \node[below,text width=14cm,align=center] at (7,-1.5) {
        \textbf{Clinical Application:} PCA reduces multiple patient characteristics to two principal components that capture 68\% of the variance. Clear patient clusters emerge, each requiring different dosing strategies. Orange arrows show how original clinical variables (age, creatinine, weight, albumin) contribute to the principal components.
    };
\end{tikzpicture}
\caption{Principal Component Analysis of Patient Data: PCA visualization reveals natural patient clusters in high-dimensional clinical space. Each cluster represents patients with similar characteristics who may require similar treatment approaches. The loading vectors show how original clinical variables contribute to the principal components, providing clinical interpretability.}
\label{fig:pca_patient_clusters}
\end{figure>

% Neural Network Architecture Diagrams

\begin{figure}[H]
\centering
\begin{tikzpicture}[
    scale=0.8,
    neuron/.style={circle, minimum size=1.2cm, draw=black, fill=blue!20, font=\small},
    input/.style={circle, minimum size=1cm, draw=black, fill=green!20, font=\small},
    output/.style={circle, minimum size=1.2cm, draw=black, fill=red!20, font=\small},
    hidden/.style={circle, minimum size=1cm, draw=black, fill=yellow!20, font=\small}
]

    % Input layer (patient characteristics)
    \node (i1) [input] at (0,6) {Age};
    \node (i2) [input] at (0,4.5) {Weight};
    \node (i3) [input] at (0,3) {CrCl};
    \node (i4) [input] at (0,1.5) {Albumin};
    \node (i5) [input] at (0,0) {Comorbid};
    
    % First hidden layer (feature extraction)
    \node (h11) [hidden] at (3,5.5) {$h_1$};
    \node (h12) [hidden] at (3,4) {$h_2$};
    \node (h13) [hidden] at (3,2.5) {$h_3$};
    \node (h14) [hidden] at (3,1) {$h_4$};
    
    % Second hidden layer (pattern recognition)
    \node (h21) [hidden] at (6,4.5) {$h_5$};
    \node (h22) [hidden] at (6,3) {$h_6$};
    \node (h23) [hidden] at (6,1.5) {$h_7$};
    
    % Output layer (dosing recommendations)
    \node (o1) [output] at (9,4) {Dose};
    \node (o2) [output] at (9,2) {Interval};
    
    % Connections (showing only some for clarity)
    \foreach \i in {1,2,3,4,5} {
        \foreach \j in {1,2,3,4} {
            \draw[->] (i\i) -- (h1\j);
        }
    }
    
    \foreach \i in {1,2,3,4} {
        \foreach \j in {1,2,3} {
            \draw[->] (h1\i) -- (h2\j);
        }
    }
    
    \foreach \i in {1,2,3} {
        \draw[->] (h2\i) -- (o1);
        \draw[->] (h2\i) -- (o2);
    }
    
    % Layer labels
    \node[below] at (0,-1) {\textbf{Input Layer}\\Patient Data};
    \node[below] at (3,-1) {\textbf{Hidden Layer 1}\\Feature Extraction};
    \node[below] at (6,-1) {\textbf{Hidden Layer 2}\\Pattern Recognition};
    \node[below] at (9,-1) {\textbf{Output Layer}\\Dosing Decision};
    
    % Activation function visualization
    \begin{scope}[xshift=12cm, yshift=3cm, scale=0.6]
        \begin{axis}[
            width=4cm,
            height=3cm,
            xlabel={$z$},
            ylabel={$\sigma(z)$},
            xmin=-4, xmax=4,
            ymin=0, ymax=1,
            grid=minor,
            title={Sigmoid Activation}
        ]
        \addplot[blue,thick,domain=-4:4,samples=50] {1/(1+exp(-x))};
        \end{axis}
    \end{scope}
    
    % Mathematical representation
    \node[below,text width=14cm,align=center] at (4.5,-3) {
        \textbf{Mathematical Operations:} 
        $h_j^{(1)} = \sigma\left(\sum_{i=1}^{5} w_{ij}^{(1)} x_i + b_j^{(1)}\right)$ \quad
        $h_k^{(2)} = \sigma\left(\sum_{j=1}^{4} w_{jk}^{(2)} h_j^{(1)} + b_k^{(2)}\right)$ \quad
        $y_l = \sum_{k=1}^{3} w_{kl}^{(3)} h_k^{(2)} + b_l^{(3)}$
    };
\end{tikzpicture}
\caption{Neural Network Architecture for Personalized Dosing: A multi-layer neural network processes patient characteristics through hidden layers to generate dosing recommendations. The first hidden layer extracts features from patient data, the second recognizes patterns, and the output layer produces specific dosing parameters. The sigmoid activation function (inset) introduces non-linearity essential for complex pattern recognition.}
\label{fig:dosing_neural_network}
\end{figure}

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.9]
    % Convolutional layer visualization for drug molecular structure analysis
    
    % Input: Molecular structure representation
    \begin{scope}
        \draw[thick] (0,0) rectangle (3,3);
        \fill[blue!20] (0,0) rectangle (3,3);
        \node at (1.5,1.5) {\textbf{Molecular}\\[-0.2em]\textbf{Structure}\\[-0.2em]\textbf{Matrix}};
        \node[below] at (1.5,-0.3) {Input: 64×64};
    \end{scope}
    
    % Convolution operation
    \begin{scope}[xshift=4.5cm]
        % Filter
        \draw[thick,red] (0,2) rectangle (1,3);
        \fill[red!30] (0,2) rectangle (1,3);
        \node[red] at (0.5,2.5) {Filter};
        
        % Convolution symbol
        \node at (0.5,1) {$*$};
        
        % Feature map
        \draw[thick] (0,0) rectangle (2,2);
        \fill[green!20] (0,0) rectangle (2,2);
        \node at (1,1) {\textbf{Feature}\\[-0.2em]\textbf{Map}};
        \node[below] at (1,-0.3) {32×32};
    \end{scope}
    
    % Multiple feature maps
    \begin{scope}[xshift=8cm]
        \foreach \i in {0,0.2,0.4} {
            \draw[thick] (\i,\i) rectangle (2+\i,2+\i);
            \fill[yellow!20] (\i,\i) rectangle (2+\i,2+\i);
        }
        \node at (1.2,1.2) {\textbf{Multiple}\\[-0.2em]\textbf{Features}};
        \node[below] at (1.2,-0.3) {32×32×16};
    \end{scope}
    
    % Pooling layer
    \begin{scope}[xshift=12cm]
        \draw[thick] (0,0) rectangle (1.5,1.5);
        \fill[purple!20] (0,0) rectangle (1.5,1.5);
        \node at (0.75,0.75) {\textbf{Pooled}\\[-0.2em]\textbf{Features}};
        \node[below] at (0.75,-0.3) {16×16×16};
    \end{scope}
    
    % Arrows
    \draw[->,thick] (3.2,1.5) -- (4.3,1.5);
    \draw[->,thick] (6.7,1.5) -- (7.8,1.5);
    \draw[->,thick] (10.7,1.5) -- (11.8,1.5);
    
    % Labels
    \node[above] at (3.75,2.5) {Convolution};
    \node[above] at (7.25,2.5) {Multiple Filters};
    \node[above] at (11.25,2.5) {Max Pooling};
    
    % Clinical interpretation
    \node[below,text width=14cm,align=center] at (6.5,-2) {
        \textbf{Drug Discovery Application:} Convolutional neural networks analyze molecular structures by detecting local chemical patterns (functional groups, bonds) through convolution filters. Multiple feature maps capture different molecular properties, and pooling reduces dimensionality while preserving important structural information for drug-target interaction prediction.
    };
\end{tikzpicture}
\caption{Convolutional Neural Network for Molecular Analysis: CNNs process molecular structure data through convolution (pattern detection), multiple feature extraction, and pooling (dimensionality reduction). This architecture is particularly effective for analyzing chemical structures and predicting drug properties, as it can identify local molecular patterns that determine biological activity.}
\label{fig:cnn_molecular}
\end{figure>

% Optimization Process Visualizations

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Training Epoch},
        ylabel={Loss Function Value},
        xmin=0, xmax=100,
        ymin=0, ymax=2,
        grid=major,
        legend pos=north east,
        title={Neural Network Training: Loss Function Optimization}
    ]
    
    % Training loss (with noise)
    \addplot[blue,thick,mark=none] coordinates {
        (0,1.8) (5,1.6) (10,1.4) (15,1.25) (20,1.1) (25,0.98) (30,0.87) (35,0.78) (40,0.71) (45,0.65) 
        (50,0.6) (55,0.56) (60,0.53) (65,0.51) (70,0.49) (75,0.48) (80,0.47) (85,0.46) (90,0.45) (95,0.44) (100,0.43)
    };
    \addlegendentry{Training Loss};
    
    % Validation loss (smoother, with overfitting)
    \addplot[red,thick,mark=none] coordinates {
        (0,1.9) (5,1.65) (10,1.42) (15,1.22) (20,1.05) (25,0.92) (30,0.82) (35,0.74) (40,0.68) (45,0.63) 
        (50,0.59) (55,0.57) (60,0.56) (65,0.56) (70,0.57) (75,0.58) (80,0.60) (85,0.62) (90,0.64) (95,0.66) (100,0.68)
    };
    \addlegendentry{Validation Loss};
    
    % Optimal stopping point
    \draw[dashed,green,thick] (axis cs:60,0) -- (axis cs:60,2);
    \node[green] at (axis cs:65,1.5) {Optimal\\Stopping};
    
    % Overfitting region
    \fill[red,opacity=0.1] (axis cs:60,0) rectangle (axis cs:100,2);
    \node[red] at (axis cs:80,1.2) {Overfitting\\Region};
    
    % Learning phases
    \draw[<->,thick] (axis cs:0,0.1) -- (axis cs:30,0.1);
    \node[below] at (axis cs:15,0.05) {Rapid Learning};
    
    \draw[<->,thick] (axis cs:30,0.1) -- (axis cs:60,0.1);
    \node[below] at (axis cs:45,0.05) {Fine-tuning};
    
    \end{axis}
    
    % Clinical interpretation
    \node[below,text width=14cm,align=center] at (7,-1.5) {
        \textbf{Clinical Relevance:} Training curves show how neural networks learn from pharmaceutical data. The gap between training and validation loss indicates overfitting - the model memorizes training data but fails to generalize to new patients. Early stopping prevents overfitting, ensuring the model performs well on unseen clinical cases.
    };
\end{tikzpicture}
\caption{Neural Network Training Dynamics: Training and validation loss curves show the learning process. Initially, both losses decrease rapidly as the network learns patterns. The optimal stopping point occurs when validation loss starts increasing (overfitting). This monitoring is crucial for developing reliable clinical prediction models.}
\label{fig:training_dynamics}
\end{figure>

\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % Gradient descent visualization in 2D parameter space
    \begin{axis}[
        width=12cm,
        height=10cm,
        xlabel={Parameter 1 (e.g., Weight $w_1$)},
        ylabel={Parameter 2 (e.g., Weight $w_2$)},
        xmin=-3, xmax=3,
        ymin=-3, ymax=3,
        grid=major,
        view={0}{90},
        title={Gradient Descent Optimization in Parameter Space}
    ]
    
    % Contour lines representing loss function
    \addplot[contour gnuplot={levels={0.5,1,2,4,8,16}}, thick] {x^2 + y^2 + 0.5*x*y + 0.1*x^3};
    
    % Gradient descent path
    \addplot[thick,red,mark=*,mark size=2pt] coordinates {
        (2.5,2) (2.1,1.6) (1.7,1.3) (1.4,1.0) (1.1,0.8) (0.8,0.6) (0.6,0.4) (0.4,0.3) (0.2,0.2) (0.1,0.1) (0,0)
    };
    
    % Starting point
    \addplot[mark=*,mark size=4pt,blue] coordinates {(2.5,2)};
    \node[blue] at (axis cs:2.7,2.2) {Start};
    
    % Optimal point
    \addplot[mark=*,mark size=4pt,green] coordinates {(0,0)};
    \node[green] at (axis cs:0.3,0.3) {Optimum};
    
    % Gradient arrows (showing direction of steepest descent)
    \foreach \x/\y/\dx/\dy in {2.5/2/-0.3/-0.25, 1.7/1.3/-0.25/-0.2, 0.8/0.6/-0.15/-0.12} {
        \draw[->,thick,purple] (axis cs:\x,\y) -- (axis cs:\x+\dx,\y+\dy);
    }
    \node[purple] at (axis cs:1.5,-2.5) {Gradient Vectors};
    
    \end{axis}
    
    % Learning rate comparison
    \begin{scope}[xshift=13cm, yshift=2cm]
        \node at (0,3) {\textbf{Learning Rate Effects}};
        
        % Too small
        \draw[blue,thick] (0,2) -- (0.1,2) -- (0.2,2) -- (0.3,2);
        \node[blue,right] at (0.4,2) {Too small: slow};
        
        % Just right
        \draw[green,thick] (0,1) -- (0.3,1) -- (0.5,1) -- (0.6,1);
        \node[green,right] at (0.7,1) {Optimal: steady};
        
        % Too large
        \draw[red,thick] (0,0) -- (0.5,0) -- (-0.2,0) -- (0.8,0) -- (-0.5,0);
        \node[red,right] at (0.9,0) {Too large: oscillating};
    \end{scope}
    
    % Clinical interpretation
    \node[below,text width=14cm,align=center] at (6,-2) {
        \textbf{Pharmaceutical Optimization:} Gradient descent finds optimal neural network parameters by following the steepest descent direction (purple arrows). The contour lines represent prediction error levels. Learning rate controls step size - too small leads to slow convergence, too large causes oscillation. This optimization process is analogous to dose titration in clinical practice.
    };
\end{tikzpicture}
\caption{Gradient Descent Optimization Visualization: The optimization process navigates the parameter space (contour plot) from initial values (blue) to optimal values (green) by following gradient directions (purple arrows). The path shows how neural networks iteratively improve their parameters to minimize prediction errors, similar to how clinicians adjust treatments based on patient response.}
\label{fig:gradient_descent}
\end{figure>