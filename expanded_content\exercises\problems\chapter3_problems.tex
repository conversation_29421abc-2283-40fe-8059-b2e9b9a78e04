% Chapter 3 Practice Problems
% Functions and Graphs in Pharmaceutical Context

\section{Chapter 3 Practice Problems}

\subsection{Problems for Section 3.1: Functions as Mathematical Models of Drug Action}

\begin{problem}{3.1.1}{Pharmacokinetic Function Analysis}
A drug follows first-order elimination with the concentration function:
$$C(t) = C_0 e^{-kt}$$

Where $C_0 = 100$ mg/L and $k = 0.2$ hr$^{-1}$.

\textbf{Part A:} Calculate the domain and range of this function.

\textbf{Part B:} Find $C(3)$ and interpret its meaning.

\textbf{Part C:} Determine when the concentration drops to 25 mg/L.

\textbf{Part D:} Calculate the rate of concentration change at $t = 2$ hours using $\frac{dC}{dt}$.
\end{problem}

\begin{problem}{3.1.2}{Dose-Response Function Properties}
A drug's dose-response relationship follows the Hill equation:
$$E(D) = \frac{E_{max} \cdot D^n}{EC_{50}^n + D^n}$$

With parameters: $E_{max} = 100$, $EC_{50} = 10$ mg, $n = 2$.

\textbf{Part A:} Determine the domain and range of this function.

\textbf{Part B:} Calculate $E(5)$, $E(10)$, and $E(20)$.

\textbf{Part C:} Find the dose that produces 75\% of maximum effect.

\textbf{Part D:} Analyze the function's behavior as $D \to 0$ and $D \to \infty$.
\end{problem}

\begin{problem}{3.1.3}{Pharmacokinetic Function Composition}
Consider the absorption and elimination processes:
- Absorption: $A(t) = D \cdot k_a \cdot e^{-k_a t}$
- Elimination: $E(A) = k_e \cdot A$

Where $D = 500$ mg, $k_a = 1.5$ hr$^{-1}$, $k_e = 0.3$ hr$^{-1}$.

\textbf{Part A:} Write the composite function $E(A(t))$.

\textbf{Part B:} Calculate the elimination rate at $t = 1$ hour.

\textbf{Part C:} Find when the elimination rate is maximum.

\textbf{Part D:} Interpret the biological meaning of function composition in this context.
\end{problem}

\begin{problem}{3.1.4}{Multi-Compartment Function Analysis}
A two-compartment model has the concentration function:
$$C(t) = A e^{-\alpha t} + B e^{-\beta t}$$

With $A = 80$ mg/L, $B = 20$ mg/L, $\alpha = 2.0$ hr$^{-1}$, $\beta = 0.1$ hr$^{-1}$.

\textbf{Part A:} Calculate $C(0)$ and verify it equals the initial dose/volume.

\textbf{Part B:} Determine the distribution and elimination half-lives.

\textbf{Part C:} Find when the concentration equals 30 mg/L.

\textbf{Part D:} Calculate the area under the curve (AUC) from $t = 0$ to $t = \infty$.
\end{problem}

\begin{problem}{3.1.5}{Bioavailability Function Analysis}
The bioavailability function for an oral formulation is:
$$F(t) = 1 - e^{-k_a t}$$

Where $k_a = 0.8$ hr$^{-1}$.

\textbf{Part A:} Calculate $F(1)$, $F(3)$, and $F(5)$ hours.

\textbf{Part B:} Find when 90\% of the dose is absorbed.

\textbf{Part C:} Calculate the absorption rate function $\frac{dF}{dt}$.

\textbf{Part D:} Determine when the absorption rate is maximum.
\end{problem}

\subsection{Problems for Section 3.2: Linear vs. Non-Linear Functions in Drug Action}

\begin{problem}{3.2.1}{Linear vs. Non-Linear Clearance}
Compare two clearance models:
- Linear: $CL_L(C) = 5.0$ L/hr (constant)
- Non-linear: $CL_{NL}(C) = \frac{V_{max}}{K_m + C}$ with $V_{max} = 400$ mg/hr, $K_m = 20$ mg/L

\textbf{Part A:} Calculate clearance for both models at concentrations 5, 20, and 50 mg/L.

\textbf{Part B:} Plot the clearance vs. concentration for both models.

\textbf{Part C:} At what concentration do both models give the same clearance?

\textbf{Part D:} Explain the clinical implications of non-linear clearance.
\end{problem}

\begin{problem}{3.2.2}{Michaelis-Menten Kinetics Analysis}
A drug follows Michaelis-Menten elimination:
$$\frac{dC}{dt} = -\frac{V_{max} \cdot C}{K_m + C}$$

With $V_{max} = 300$ mg/hr and $K_m = 15$ mg/L.

\textbf{Part A:} Calculate the elimination rate at $C = 5$, $15$, and $45$ mg/L.

\textbf{Part B:} Determine the concentration at which elimination is half-maximal.

\textbf{Part C:} Calculate the percentage of $V_{max}$ achieved at $C = 3K_m$.

\textbf{Part D:} Compare with linear elimination ($k = 0.2$ hr$^{-1}$) at these concentrations.
\end{problem}

\begin{problem}{3.2.3}{Sigmoid Dose-Response Analysis}
A drug shows sigmoid dose-response behavior:
$$E(D) = \frac{100}{1 + e^{-s(D - D_{50})}}$$

Where $s = 0.1$ (mg)$^{-1}$ and $D_{50} = 50$ mg.

\textbf{Part A:} Calculate responses at doses 20, 40, 50, 60, and 80 mg.

\textbf{Part B:} Find the doses producing 10\%, 50\%, and 90\% responses.

\textbf{Part C:} Calculate the slope at $D = D_{50}$.

\textbf{Part D:} Compare with a linear model $E = 2D$ over the range 0-100 mg.
\end{problem}

\begin{problem}{3.2.4}{Competitive Inhibition Analysis}
In the presence of a competitive inhibitor, enzyme kinetics become:
$$v = \frac{V_{max} \cdot [S]}{K_m(1 + \frac{[I]}{K_i}) + [S]}$$

With $V_{max} = 200$ μmol/min, $K_m = 10$ μM, $K_i = 5$ μM.

\textbf{Part A:} Calculate reaction velocity at $[S] = 20$ μM with $[I] = 0$, $5$, and $15$ μM.

\textbf{Part B:} Determine the apparent $K_m$ for each inhibitor concentration.

\textbf{Part C:} Calculate the fold-increase in substrate needed to achieve 50\% $V_{max}$ with $[I] = 10$ μM.

\textbf{Part D:} Plot $v$ vs. $[S]$ for different inhibitor concentrations.
\end{problem}

\begin{problem}{3.2.5}{Allosteric Modulation Analysis}
An allosteric drug interaction follows:
$$E = \frac{E_{max} \cdot D^n}{EC_{50}^n(1 + \frac{M}{K_M})^n + D^n}$$

Where $M$ is modulator concentration, $K_M = 2$ μM, and other parameters are $E_{max} = 100$, $EC_{50} = 10$ μM, $n = 2$.

\textbf{Part A:} Calculate the effect at $D = 15$ μM with $M = 0$, $2$, and $6$ μM.

\textbf{Part B:} Determine how the apparent $EC_{50}$ changes with modulator concentration.

\textbf{Part C:} Calculate the modulator concentration that doubles the $EC_{50}$.

\textbf{Part D:} Compare this with competitive inhibition at equivalent effect levels.
\end{problem}

\subsection{Problems for Section 3.3: Function Composition and Neural Networks}

\begin{problem}{3.3.1}{PK-PD Model Composition}
A pharmacokinetic-pharmacodynamic model combines:
- PK: $C(t) = 50 e^{-0.2t}$ mg/L
- PD: $E(C) = \frac{80C}{20 + C}$

\textbf{Part A:} Write the composite function $E(C(t))$.

\textbf{Part B:} Calculate the effect at $t = 2$, $6$, and $12$ hours.

\textbf{Part C:} Find when the effect drops to 50\% of its initial value.

\textbf{Part D:} Calculate $\frac{dE}{dt}$ using the chain rule.
\end{problem}

\begin{problem}{3.3.2}{Multi-Layer Function Composition}
A three-layer neural network processes drug data through:
- Layer 1: $f_1(x) = \max(0, 2x - 1)$ (ReLU activation)
- Layer 2: $f_2(y) = \frac{1}{1 + e^{-3y}}$ (sigmoid activation)
- Layer 3: $f_3(z) = 5z$ (linear output)

\textbf{Part A:} Calculate the composite function $f_3(f_2(f_1(x)))$ for $x = 1$.

\textbf{Part B:} Evaluate the network output for inputs $x = 0$, $0.5$, $1$, and $2$.

\textbf{Part C:} Find the input value that produces an output of 2.5.

\textbf{Part D:} Calculate the derivative of the composite function at $x = 1$.
\end{problem}

\begin{problem}{3.3.3}{Nested Pharmacological Functions}
A complex drug interaction involves nested functions:
$$R(D_1, D_2) = f_3(f_2(f_1(D_1) + D_2))$$

Where:
- $f_1(D_1) = \sqrt{D_1}$ (absorption)
- $f_2(x) = \frac{x}{2 + x}$ (receptor binding)
- $f_3(y) = 100y$ (response amplification)

\textbf{Part A:} Calculate $R(16, 1)$.

\textbf{Part B:} Find $D_1$ when $D_2 = 2$ and $R = 25$.

\textbf{Part C:} Calculate $\frac{\partial R}{\partial D_1}$ at $(D_1, D_2) = (9, 1)$.

\textbf{Part D:} Interpret the biological meaning of each function layer.
\end{problem}

\begin{problem}{3.3.4}{Sequential Drug Processing}
A drug undergoes sequential metabolism:
- Phase I: $M_1(D) = 0.8D$ (80\% metabolized)
- Phase II: $M_2(x) = 0.6x$ (60\% conjugated)
- Excretion: $E(y) = 1 - e^{-0.1y}$ (fraction excreted)

\textbf{Part A:} Write the composite function for total processing.

\textbf{Part B:} Calculate the fraction excreted for initial doses of 100, 200, and 500 mg.

\textbf{Part C:} Find the dose that results in 50\% excretion.

\textbf{Part D:} Calculate the sensitivity $\frac{dE}{dD}$ at $D = 300$ mg.
\end{problem}

\subsection{Problems for Section 3.4: Optimization and Function Minimization}

\begin{problem}{3.4.1}{Dose Optimization}
Find the optimal dose to minimize the cost function:
$$J(D) = (E(D) - E_{target})^2 + \lambda D^2$$

Where $E(D) = \frac{100D}{50 + D}$, $E_{target} = 60$, and $\lambda = 0.01$.

\textbf{Part A:} Calculate $J(D)$ for doses 50, 75, 100, and 150 mg.

\textbf{Part B:} Find $\frac{dJ}{dD}$ and set it equal to zero.

\textbf{Part C:} Solve for the optimal dose analytically or numerically.

\textbf{Part D:} Verify this is a minimum using the second derivative test.
\end{problem}

\begin{problem}{3.4.2}{Multi-Drug Optimization}
Optimize doses for two drugs to minimize:
$$J(D_1, D_2) = (E_1 + E_2 - 80)^2 + 0.1(D_1^2 + D_2^2)$$

Where $E_1 = \frac{60D_1}{20 + D_1}$ and $E_2 = \frac{40D_2}{10 + D_2}$.

\textbf{Part A:} Calculate the partial derivatives $\frac{\partial J}{\partial D_1}$ and $\frac{\partial J}{\partial D_2}$.

\textbf{Part B:} Set up the system of equations for the critical point.

\textbf{Part C:} Solve numerically for optimal doses $(D_1^*, D_2^*)$.

\textbf{Part D:} Calculate the minimum cost and verify it's a minimum.
\end{problem}

\begin{problem}{3.4.3}{Gradient Descent for Parameter Estimation}
Use gradient descent to minimize the sum of squared errors:
$$SSE(\theta) = \sum_{i=1}^{4} (y_i - \theta x_i)^2$$

With data: $(x_1, y_1) = (1, 3)$, $(x_2, y_2) = (2, 5)$, $(x_3, y_3) = (3, 7)$, $(x_4, y_4) = (4, 9)$.

\textbf{Part A:} Calculate $\frac{dSSE}{d\theta}$.

\textbf{Part B:} Starting with $\theta_0 = 1$ and learning rate $\alpha = 0.1$, perform 3 iterations.

\textbf{Part C:} Calculate the SSE at each iteration.

\textbf{Part D:} Compare with the analytical solution $\theta^* = \frac{\sum x_i y_i}{\sum x_i^2}$.
\end{problem}

\begin{problem}{3.4.4}{Constrained Optimization in Drug Formulation}
Maximize bioavailability $F = 0.8x + 0.6y$ subject to:
- Cost constraint: $2x + 3y \leq 100$
- Stability constraint: $x + y \geq 20$
- Non-negativity: $x, y \geq 0$

\textbf{Part A:} Graph the feasible region.

\textbf{Part B:} Identify the corner points of the feasible region.

\textbf{Part C:} Evaluate the objective function at each corner point.

\textbf{Part D:} Determine the optimal formulation $(x^*, y^*)$ and maximum bioavailability.
\end{problem}

\subsection{Problems for Section 3.5: Probability and Statistical Functions}

\begin{problem}{3.5.1}{Probability Density Functions in Pharmacology}
A drug's plasma concentration follows a log-normal distribution:
$$f(c) = \frac{1}{c\sigma\sqrt{2\pi}} e^{-\frac{(\ln c - \mu)^2}{2\sigma^2}}$$

With $\mu = 3$ and $\sigma = 0.5$.

\textbf{Part A:} Calculate the probability density at concentrations 10, 20, and 30 mg/L.

\textbf{Part B:} Find the median concentration (where $\ln c = \mu$).

\textbf{Part C:} Calculate the probability that concentration exceeds 25 mg/L.

\textbf{Part D:} Determine the 95th percentile concentration.
\end{problem}

\begin{problem}{3.5.2}{Bayesian Inference for Drug Dosing}
Prior belief about clearance follows $CL \sim N(5, 1)$. After observing concentration data, the likelihood is proportional to $e^{-2(CL-6)^2}$.

\textbf{Part A:} Write the posterior distribution (assume normal form).

\textbf{Part B:} Calculate the posterior mean and variance.

\textbf{Part C:} Compare the posterior mean with the prior mean and likelihood mode.

\textbf{Part D:} Calculate the 95\% credible interval for clearance.
\end{problem}

\begin{problem}{3.5.3}{Survival Function Analysis}
Drug efficacy over time follows the survival function:
$$S(t) = e^{-\lambda t}$$

Where $\lambda = 0.05$ day$^{-1}$.

\textbf{Part A:} Calculate the probability of treatment success at 10, 20, and 30 days.

\textbf{Part B:} Find the median survival time.

\textbf{Part C:} Calculate the hazard function $h(t) = \frac{f(t)}{S(t)}$.

\textbf{Part D:} Determine the probability of failure between days 15 and 25.
\end{problem}

\begin{problem}{3.5.4}{Mixture Model for Drug Response}
Patient response follows a mixture of two normal distributions:
$$f(x) = 0.7 \cdot N(20, 4) + 0.3 \cdot N(35, 9)$$

\textbf{Part A:} Calculate the overall mean and variance of the mixture.

\textbf{Part B:} Find the probability density at response values 15, 25, and 40.

\textbf{Part C:} Calculate the probability that a patient is a "good responder" (from the second component) given a response of 30.

\textbf{Part D:} Determine the response threshold that classifies 80\% of patients correctly.
\end{problem}

\begin{problem}{3.5.5}{Logistic Regression for Adverse Events}
The probability of adverse events follows:
$$P(AE) = \frac{1}{1 + e^{-(\beta_0 + \beta_1 \cdot dose + \beta_2 \cdot age)}}$$

With $\beta_0 = -3$, $\beta_1 = 0.02$, $\beta_2 = 0.05$.

\textbf{Part A:} Calculate the probability of adverse events for a 60-year-old patient receiving 100 mg.

\textbf{Part B:} Find the dose that gives 10\% probability of adverse events for a 50-year-old patient.

\textbf{Part C:} Calculate the odds ratio for a 10 mg dose increase.

\textbf{Part D:} Determine the age at which adverse event probability doubles for a fixed dose of 80 mg.
\end{problem}