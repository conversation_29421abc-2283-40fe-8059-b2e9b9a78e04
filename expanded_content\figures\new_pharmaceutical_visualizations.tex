% New Pharmaceutical Visualizations
% Contains only the NEW figures needed for integration

% Figure: Dose-Response Models Comparison
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Drug Concentration (mg/L)},
        ylabel={Effect (\% of Maximum)},
        xmin=0, xmax=10,
        ymin=0, ymax=110,
        grid=major,
        legend pos=south east,
        title={Dose-Response Model Comparison}
    ]
    
    % Linear response
    \addplot[blue, thick, domain=0:10, samples=100] {10*x};
    \addlegendentry{Linear Response}
    
    % <PERSON><PERSON>-<PERSON>ten (hyperbolic)
    \addplot[red, thick, domain=0:10, samples=100] {100*x/(2+x)};
    \addlegendentry{Michaelis-Menten}
    
    % Sigmoid (Hill equation)
    \addplot[green, thick, domain=0:10, samples=100] {100*x^2/(3^2+x^2)};
    \addlegendentry{Sigmoid (Hill)}
    
    % Logarithmic
    \addplot[purple, thick, domain=0.1:10, samples=100] {30*ln(x+1)};
    \addlegendentry{Logarithmic}
    
    % EC50 markers
    \draw[dashed, gray] (axis cs:2,0) -- (axis cs:2,50);
    \draw[dashed, gray] (axis cs:3,0) -- (axis cs:3,50);
    \node[gray] at (axis cs:2.5,15) {$EC_{50}$};
    
    \end{axis}
\end{tikzpicture}
\caption{Dose-Response Model Comparison: Different mathematical models describe how drug effects vary with concentration. Linear responses show direct proportionality, Michaelis-Menten kinetics demonstrate saturation, sigmoid curves capture cooperative binding, and logarithmic responses show diminishing returns. The $EC_{50}$ represents the concentration producing 50\% of maximum effect.}
\label{fig:dose_response_models}
\end{figure}

% Figure: PK-PD Relationships
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Time (hours)},
        ylabel={Concentration/Effect},
        xmin=0, xmax=24,
        ymin=0, ymax=12,
        grid=major,
        legend pos=north east,
        title={Pharmacokinetic-Pharmacodynamic Relationships}
    ]
    
    % PK profile (concentration)
    \addplot[blue, thick, domain=0:24, samples=200] {10*exp(-0.2*x)};
    \addlegendentry{Drug Concentration}
    
    % Direct PD effect
    \addplot[red, thick, domain=0:24, samples=200] {8*exp(-0.2*x)};
    \addlegendentry{Direct Effect}
    
    % Delayed PD effect (effect compartment)
    \addplot[green, thick, domain=0:24, samples=200] {6*x*exp(-0.15*x)};
    \addlegendentry{Delayed Effect}
    
    % Tolerance effect
    \addplot[purple, thick, domain=0:24, samples=200] {4*exp(-0.3*x)*(1+0.1*x)};
    \addlegendentry{Tolerance Effect}
    
    \end{axis}
\end{tikzpicture}
\caption{Pharmacokinetic-Pharmacodynamic Relationships: The temporal relationship between drug concentration and effect varies by mechanism. Direct effects follow concentration immediately, delayed effects show hysteresis due to distribution, and tolerance effects demonstrate receptor desensitization over time.}
\label{fig:pkpd_relationships}
\end{figure}

% Figure: Patient Similarity Visualization
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    % Patient similarity matrix heatmap (simplified)
    \fill[red!80!blue] (0,0) rectangle (0.8,0.8); \node at (0.4,0.4) {100};
    \fill[red!60!blue] (1,0) rectangle (1.8,0.8); \node at (1.4,0.4) {80};
    \fill[red!40!blue] (2,0) rectangle (2.8,0.8); \node at (2.4,0.4) {60};
    \fill[red!20!blue] (3,0) rectangle (3.8,0.8); \node at (3.4,0.4) {40};
    \fill[red!0!blue] (4,0) rectangle (4.8,0.8); \node at (4.4,0.4) {20};
    
    \fill[red!60!blue] (0,1) rectangle (0.8,1.8); \node at (0.4,1.4) {80};
    \fill[red!80!blue] (1,1) rectangle (1.8,1.8); \node at (1.4,1.4) {100};
    \fill[red!60!blue] (2,1) rectangle (2.8,1.8); \node at (2.4,1.4) {80};
    \fill[red!40!blue] (3,1) rectangle (3.8,1.8); \node at (3.4,1.4) {60};
    \fill[red!20!blue] (4,1) rectangle (4.8,1.8); \node at (4.4,1.4) {40};
    
    \fill[red!40!blue] (0,2) rectangle (0.8,2.8); \node at (0.4,2.4) {60};
    \fill[red!60!blue] (1,2) rectangle (1.8,2.8); \node at (1.4,2.4) {80};
    \fill[red!80!blue] (2,2) rectangle (2.8,2.8); \node at (2.4,2.4) {100};
    \fill[red!60!blue] (3,2) rectangle (3.8,2.8); \node at (3.4,2.4) {80};
    \fill[red!40!blue] (4,2) rectangle (4.8,2.8); \node at (4.4,2.4) {60};
    
    \fill[red!20!blue] (0,3) rectangle (0.8,3.8); \node at (0.4,3.4) {40};
    \fill[red!40!blue] (1,3) rectangle (1.8,3.8); \node at (1.4,3.4) {60};
    \fill[red!60!blue] (2,3) rectangle (2.8,3.8); \node at (2.4,3.4) {80};
    \fill[red!80!blue] (3,3) rectangle (3.8,3.8); \node at (3.4,3.4) {100};
    \fill[red!60!blue] (4,3) rectangle (4.8,3.8); \node at (4.4,3.4) {80};
    
    \fill[red!0!blue] (0,4) rectangle (0.8,4.8); \node at (0.4,4.4) {20};
    \fill[red!20!blue] (1,4) rectangle (1.8,4.8); \node at (1.4,4.4) {40};
    \fill[red!40!blue] (2,4) rectangle (2.8,4.8); \node at (2.4,4.4) {60};
    \fill[red!60!blue] (3,4) rectangle (3.8,4.8); \node at (3.4,4.4) {80};
    \fill[red!80!blue] (4,4) rectangle (4.8,4.8); \node at (4.4,4.4) {100};
    
    % Labels
    \foreach \i in {0,1,2,3,4} {
        \node[below] at (\i+0.4,-0.2) {P\i};
        \node[left] at (-0.2,\i+0.4) {P\i};
    }
    
    \node[above] at (2.4,5.2) {Patient Similarity Matrix (\%)};
    
    % Color bar (simplified)
    \fill[blue] (6,0) rectangle (6.5,0.4);
    \fill[red!20!blue] (6,0.4) rectangle (6.5,0.8);
    \fill[red!40!blue] (6,0.8) rectangle (6.5,1.2);
    \fill[red!60!blue] (6,1.2) rectangle (6.5,1.6);
    \fill[red!80!blue] (6,1.6) rectangle (6.5,2.0);
    \fill[red] (6,2.0) rectangle (6.5,2.4);
    \node[right] at (6.6,2) {Similarity};
    \node[right] at (6.6,0.2) {0\%};
    \node[right] at (6.6,3.8) {100\%};
    
\end{tikzpicture}
\caption{Patient Similarity Visualization: A similarity matrix shows how closely related patients are based on clinical characteristics. High similarity (red) indicates patients with similar profiles who might respond similarly to treatments, while low similarity (blue) suggests different therapeutic approaches may be needed.}
\label{fig:patient_similarity}
\end{figure}

% Figure: PCA Patient Clusters
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.2]
    % Coordinate system
    \draw[->] (-3,0) -- (4,0) node[right] {PC1 (Age/Weight)};
    \draw[->] (0,-3) -- (0,4) node[above] {PC2 (Kidney Function)};
    
    % Patient clusters
    % Elderly cluster
    \fill[red] (2.2,1.1) circle (0.08);
    \fill[red] (2.4,0.9) circle (0.08);
    \fill[red] (2.1,1.3) circle (0.08);
    \fill[red] (2.6,1.2) circle (0.08);
    \fill[red] (2.3,1.4) circle (0.08);
    \node[red] at (2.5,1.8) {Elderly};
    
    % Young healthy cluster
    \fill[blue] (-1.3,2.1) circle (0.08);
    \fill[blue] (-1.6,1.9) circle (0.08);
    \fill[blue] (-1.4,2.3) circle (0.08);
    \fill[blue] (-1.2,2.2) circle (0.08);
    \fill[blue] (-1.5,2.4) circle (0.08);
    \node[blue] at (-1.5,2.8) {Young Healthy};
    
    % Renal impairment cluster
    \fill[green] (0.6,-1.9) circle (0.08);
    \fill[green] (0.4,-2.1) circle (0.08);
    \fill[green] (0.7,-2.2) circle (0.08);
    \fill[green] (0.3,-1.8) circle (0.08);
    \fill[green] (0.8,-2.0) circle (0.08);
    \node[green] at (0.5,-2.8) {Renal Impairment};
    
    % Pediatric cluster
    \fill[purple] (-2.4,-0.4) circle (0.08);
    \fill[purple] (-2.6,-0.6) circle (0.08);
    \fill[purple] (-2.3,-0.7) circle (0.08);
    \fill[purple] (-2.5,-0.3) circle (0.08);
    \node[purple] at (-2.5,-1.2) {Pediatric};
    
    % Principal component arrows
    \draw[->,thick,gray] (0,0) -- (3,0.5) node[above right] {PC1: 65\% variance};
    \draw[->,thick,gray] (0,0) -- (-0.5,3) node[above left] {PC2: 25\% variance};
    
\end{tikzpicture}
\caption{PCA Patient Clusters: Principal Component Analysis reveals natural groupings in patient populations. PC1 captures age and weight variations, while PC2 represents kidney function. Clustering helps identify patient subgroups that may require different dosing strategies or have similar drug responses.}
\label{fig:pca_patient_clusters}
\end{figure}

% Figure: CNN Molecular Analysis
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.8]
    % Molecular structure (simplified)
    \draw[thick] (0,0) -- (1,0.5) -- (2,0) -- (3,0.5) -- (4,0) -- (5,0.5);
    \draw[thick] (1,0.5) -- (1.5,1.5) -- (2.5,1.5) -- (3,0.5);
    \draw[thick] (2.5,1.5) -- (3.5,2) -- (4.5,1.5) -- (5,0.5);
    
    % Atoms
    \foreach \pos in {(0,0), (1,0.5), (2,0), (3,0.5), (4,0), (5,0.5), (1.5,1.5), (2.5,1.5), (3.5,2), (4.5,1.5)} {
        \fill[blue] \pos circle (0.1);
    }
    
    \node[below] at (2.5,-0.5) {Drug Molecule};
    
    % Arrow
    \draw[->,thick] (6,1) -- (8,1);
    \node[above] at (7,1.2) {CNN};
    
    % Feature maps
    \draw[thick] (9,0) rectangle (11,2);
    \draw[thick] (9.3,0.2) rectangle (11.3,2.2);
    \draw[thick] (9.6,0.4) rectangle (11.6,2.4);
    
    % Simple grid pattern for feature maps
    \foreach \x in {0,1,2,3,4} {
        \foreach \y in {0,1,2,3,4} {
            \fill[red!50!white] (9.2+\x*0.32,0.2+\y*0.32) rectangle (9.4+\x*0.32,0.4+\y*0.32);
        }
    }
    \node[below] at (10,0) {Feature Maps};
    
    % Arrow
    \draw[->,thick] (12.5,1) -- (14.5,1);
    
    % Output
    \draw[thick] (15,0.5) rectangle (17,1.5);
    \node at (16,1) {Binding};
    \node at (16,0.7) {Affinity};
    \node[below] at (16,0.3) {Prediction};
    
\end{tikzpicture}
\caption{CNN Molecular Analysis: Convolutional Neural Networks analyze molecular structures by detecting local chemical patterns. The network processes molecular graphs through convolutional layers that identify functional groups and binding sites, ultimately predicting drug properties like binding affinity or toxicity.}
\label{fig:cnn_molecular}
\end{figure}

% Figure: Training Dynamics
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=14cm,
        height=10cm,
        xlabel={Training Epoch},
        ylabel={Loss/Accuracy},
        xmin=0, xmax=100,
        ymin=0, ymax=1,
        grid=major,
        legend pos=center right,
        title={Neural Network Training Dynamics}
    ]
    
    % Training loss
    \addplot[blue, thick, domain=0:100, samples=100] {0.8*exp(-0.05*x) + 0.1 + 0.02*sin(deg(x/2))};
    \addlegendentry{Training Loss}
    
    % Validation loss
    \addplot[red, thick, domain=0:100, samples=100] {0.9*exp(-0.04*x) + 0.15 + 0.03*sin(deg(x/3))};
    \addlegendentry{Validation Loss}
    
    % Training accuracy
    \addplot[green, thick, domain=0:100, samples=100] {1 - 0.7*exp(-0.06*x) - 0.05*sin(deg(x/4))};
    \addlegendentry{Training Accuracy}
    
    % Validation accuracy
    \addplot[purple, thick, domain=0:100, samples=100] {1 - 0.8*exp(-0.05*x) - 0.08*sin(deg(x/5))};
    \addlegendentry{Validation Accuracy}
    
    % Overfitting region
    \draw[dashed, gray] (axis cs:60,0) -- (axis cs:60,1);
    \node[gray] at (axis cs:70,0.8) {Overfitting};
    \node[gray] at (axis cs:70,0.75) {Region};
    
    \end{axis}
\end{tikzpicture}
\caption{Neural Network Training Dynamics: Training curves show how model performance evolves during learning. Initially, both training and validation metrics improve together. Overfitting occurs when training performance continues improving while validation performance plateaus or degrades, indicating the model is memorizing rather than generalizing.}
\label{fig:training_dynamics}
\end{figure}

% Figure: Gradient Descent Visualization
\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=1.0]
    \begin{axis}[
        width=12cm,
        height=10cm,
        xlabel={Parameter $\theta_1$},
        ylabel={Parameter $\theta_2$},
        xmin=-3, xmax=3,
        ymin=-3, ymax=3,
        view={0}{90},
        colormap/hot,
        title={Gradient Descent Optimization Path}
    ]
    
    % Cost function contours (simplified quadratic)
    \addplot3[contour gnuplot={levels={0.5,1,2,4,8}}, samples=50] {x^2 + 2*y^2};
    
    % Gradient descent path
    \addplot[blue, thick, mark=*, mark size=2pt] coordinates {
        (-2.5, 2.0)
        (-2.0, 1.5)
        (-1.5, 1.0)
        (-1.0, 0.6)
        (-0.6, 0.3)
        (-0.3, 0.1)
        (-0.1, 0.0)
        (0.0, 0.0)
    };
    
    % Starting point
    \node[blue] at (axis cs:-2.5,2.2) {Start};
    
    % Minimum point
    \node[red] at (axis cs:0.2,0.2) {Minimum};
    \fill[red] (axis cs:0,0) circle (3pt);
    
    % Gradient arrows
    \draw[->,thick,green] (axis cs:-2.0,1.5) -- (axis cs:-1.7,1.2);
    \draw[->,thick,green] (axis cs:-1.0,0.6) -- (axis cs:-0.8,0.4);
    \node[green] at (axis cs:-1.5,2.5) {Gradient Direction};
    
    \end{axis}
\end{tikzpicture}
\caption{Gradient Descent Optimization: The algorithm follows the negative gradient (steepest descent direction) to minimize the cost function. Each step moves toward lower cost values, eventually converging to the minimum. In neural networks, this process adjusts weights to minimize prediction errors.}
\label{fig:gradient_descent}
\end{figure}