% Chapter 3 Detailed Solutions
% Functions and Graphs in Pharmaceutical Context

\section{Chapter 3 Detailed Solutions}

\subsection{Solutions for Section 3.1: Functions as Mathematical Models of Drug Action}

\begin{solution}{3.1.1}{Pharmacokinetic Function Analysis}

\textbf{Part A: Domain and range}

\textbf{Strategy:} Analyze the mathematical properties of $C(t) = C_0 e^{-kt}$

\textbf{Domain:} $t \geq 0$ (time cannot be negative in pharmacokinetics)

\textbf{Range:} $(0, C_0]$ where $C_0 = 100$ mg/L
- As $t \to 0$: $C(t) \to 100$ mg/L
- As $t \to \infty$: $C(t) \to 0$ mg/L (but never reaches exactly 0)

\textbf{Range:} $(0, 100]$ mg/L

\textbf{Part B: Calculate C(3)}

\textbf{Strategy:} Substitute $t = 3$ into the function

\textbf{Solution:}
$$C(3) = 100 e^{-0.2 \times 3} = 100 e^{-0.6} = 100 \times 0.549 = 54.9 \text{ mg/L}$$

\textbf{Interpretation:} After 3 hours, the drug concentration has decreased to 54.9 mg/L, representing a 45.1% decrease from the initial concentration.

\textbf{Part C: Time to reach 25 mg/L}

\textbf{Strategy:} Solve $25 = 100 e^{-0.2t}$ for $t$

\textbf{Step 1:} Divide both sides by 100
$$0.25 = e^{-0.2t}$$

\textbf{Step 2:} Take natural logarithm
$$\ln(0.25) = -0.2t$$
$$-1.386 = -0.2t$$

\textbf{Step 3:} Solve for t
$$t = \frac{1.386}{0.2} = 6.93 \text{ hours}$$

\textbf{Verification:} $C(6.93) = 100 e^{-0.2 \times 6.93} = 100 e^{-1.386} = 100 \times 0.25 = 25$ mg/L ✓

\textbf{Part D: Rate of concentration change at t = 2}

\textbf{Strategy:} Calculate $\frac{dC}{dt} = \frac{d}{dt}[100 e^{-0.2t}] = 100(-0.2)e^{-0.2t} = -20 e^{-0.2t}$

\textbf{Solution:}
$$\frac{dC}{dt}\bigg|_{t=2} = -20 e^{-0.2 \times 2} = -20 e^{-0.4} = -20 \times 0.670 = -13.4 \text{ mg/L/hr}$$

\textbf{Clinical Interpretation:} At t = 2 hours, the concentration is decreasing at a rate of 13.4 mg/L per hour. The negative sign indicates the concentration is declining.
\end{solution}

\begin{solution}{3.1.2}{Dose-Response Function Properties}

\textbf{Part A: Domain and range}

\textbf{Strategy:} Analyze the Hill equation $E(D) = \frac{E_{max} \cdot D^n}{EC_{50}^n + D^n}$

\textbf{Domain:} $D \geq 0$ (dose cannot be negative)

\textbf{Range Analysis:}
- As $D \to 0$: $E(D) \to 0$
- As $D \to \infty$: $E(D) \to E_{max} = 100$
- The function is monotonically increasing

\textbf{Range:} $[0, 100)$ (approaches but never quite reaches 100)

\textbf{Part B: Response calculations}

\textbf{At D = 5 mg:}
$$E(5) = \frac{100 \times 5^2}{10^2 + 5^2} = \frac{100 \times 25}{100 + 25} = \frac{2500}{125} = 20.0\%$$

\textbf{At D = 10 mg:}
$$E(10) = \frac{100 \times 10^2}{10^2 + 10^2} = \frac{100 \times 100}{100 + 100} = \frac{10000}{200} = 50.0\%$$

\textbf{At D = 20 mg:}
$$E(20) = \frac{100 \times 20^2}{10^2 + 20^2} = \frac{100 \times 400}{100 + 400} = \frac{40000}{500} = 80.0\%$$

\textbf{Verification:} At $D = EC_{50} = 10$ mg, we get exactly 50% of maximum effect, confirming our calculation.

\textbf{Part C: Dose for 75% effect}

\textbf{Strategy:} Solve $75 = \frac{100 \times D^2}{100 + D^2}$ for $D$

\textbf{Step 1:} Simplify
$$0.75 = \frac{D^2}{100 + D^2}$$

\textbf{Step 2:} Cross multiply
$$0.75(100 + D^2) = D^2$$
$$75 + 0.75D^2 = D^2$$
$$75 = D^2 - 0.75D^2 = 0.25D^2$$

\textbf{Step 3:} Solve for D
$$D^2 = \frac{75}{0.25} = 300$$
$$D = \sqrt{300} = 17.3 \text{ mg}$$

\textbf{Verification:} $E(17.3) = \frac{100 \times 300}{100 + 300} = \frac{30000}{400} = 75\%$ ✓

\textbf{Part D: Asymptotic behavior}

\textbf{As D → 0:}
$$\lim_{D \to 0} E(D) = \lim_{D \to 0} \frac{100D^2}{100 + D^2} = \frac{0}{100} = 0$$

\textbf{As D → ∞:}
$$\lim_{D \to \infty} E(D) = \lim_{D \to \infty} \frac{100D^2}{100 + D^2} = \lim_{D \to \infty} \frac{100}{100/D^2 + 1} = \frac{100}{0 + 1} = 100$$

\textbf{Clinical Interpretation:} The function starts at zero effect with no drug and approaches maximum effect at very high doses, with 50% effect occurring at the EC₅₀ value.
\end{solution}

\begin{solution}{3.1.3}{Pharmacokinetic Function Composition}

\textbf{Part A: Composite function}

\textbf{Strategy:} Substitute $A(t)$ into $E(A)$ to get $E(A(t))$

\textbf{Given functions:}
- $A(t) = 500 \times 1.5 \times e^{-1.5t} = 750 e^{-1.5t}$
- $E(A) = 0.3 \times A$

\textbf{Composite function:}
$$E(A(t)) = 0.3 \times 750 e^{-1.5t} = 225 e^{-1.5t} \text{ mg/hr}$$

\textbf{Part B: Elimination rate at t = 1 hour}

\textbf{Solution:}
$$E(A(1)) = 225 e^{-1.5 \times 1} = 225 e^{-1.5} = 225 \times 0.223 = 50.2 \text{ mg/hr}$$

\textbf{Part C: Maximum elimination rate}

\textbf{Strategy:} Find when $\frac{d}{dt}[E(A(t))] = 0$

\textbf{Step 1:} Calculate the derivative
$$\frac{d}{dt}[225 e^{-1.5t}] = 225(-1.5)e^{-1.5t} = -337.5 e^{-1.5t}$$

\textbf{Analysis:} Since the derivative is always negative for $t > 0$, the elimination rate is always decreasing. The maximum occurs at $t = 0$.

\textbf{Maximum elimination rate:}
$$E(A(0)) = 225 e^{0} = 225 \text{ mg/hr}$$

\textbf{Part D: Biological interpretation}

\textbf{Function Composition Meaning:}
\begin{itemize}
\item $A(t)$ represents the amount of drug available for elimination at time $t$
\item $E(A)$ represents how much of that available drug is actually eliminated
\item The composition $E(A(t))$ gives the instantaneous elimination rate as a function of time
\end{itemize}

\textbf{Clinical Significance:} This model shows that elimination rate is highest immediately after absorption begins and decreases exponentially over time, which is consistent with first-order elimination kinetics.

\textbf{Mathematical Insight:} Function composition allows us to model complex physiological processes by linking simpler component processes (absorption → elimination).
\end{solution}

\begin{solution}{3.1.4}{Multi-Compartment Function Analysis}

\textbf{Part A: Initial concentration}

\textbf{Strategy:} Calculate $C(0)$ and verify it equals initial dose/volume

\textbf{Solution:}
$$C(0) = A e^{-\alpha \times 0} + B e^{-\beta \times 0} = A e^0 + B e^0 = A + B = 80 + 20 = 100 \text{ mg/L}$$

\textbf{Verification:} This represents the initial concentration immediately after IV bolus administration, confirming the dose was 100 mg/L × volume.

\textbf{Part B: Half-life calculations}

\textbf{Strategy:} Use $t_{1/2} = \frac{\ln(2)}{\lambda}$ for each rate constant

\textbf{Distribution half-life:}
$$t_{1/2,\alpha} = \frac{0.693}{2.0} = 0.347 \text{ hours} = 20.8 \text{ minutes}$$

\textbf{Elimination half-life:}
$$t_{1/2,\beta} = \frac{0.693}{0.1} = 6.93 \text{ hours}$$

\textbf{Clinical Interpretation:} The drug has a rapid distribution phase (21 minutes) followed by a much slower elimination phase (6.9 hours).

\textbf{Part C: Time to reach 30 mg/L}

\textbf{Strategy:} Solve $30 = 80 e^{-2t} + 20 e^{-0.1t}$ for $t$

This transcendental equation requires numerical methods. Using iterative approximation:

\textbf{Trial and error approach:}
- At $t = 1$: $C(1) = 80 e^{-2} + 20 e^{-0.1} = 80(0.135) + 20(0.905) = 10.8 + 18.1 = 28.9$ mg/L
- At $t = 0.9$: $C(0.9) = 80 e^{-1.8} + 20 e^{-0.09} = 80(0.165) + 20(0.914) = 13.2 + 18.3 = 31.5$ mg/L

\textbf{By interpolation:} $t \approx 0.95$ hours

\textbf{Part D: Area under the curve (AUC)}

\textbf{Strategy:} Calculate $AUC = \int_0^{\infty} C(t) dt = \int_0^{\infty} (A e^{-\alpha t} + B e^{-\beta t}) dt$

\textbf{Solution:}
$$AUC = \int_0^{\infty} 80 e^{-2t} dt + \int_0^{\infty} 20 e^{-0.1t} dt$$

$$= 80 \left[-\frac{1}{2} e^{-2t}\right]_0^{\infty} + 20 \left[-\frac{1}{0.1} e^{-0.1t}\right]_0^{\infty}$$

$$= 80 \left[0 - \left(-\frac{1}{2}\right)\right] + 20 \left[0 - \left(-\frac{1}{0.1}\right)\right]$$

$$= 80 \times \frac{1}{2} + 20 \times \frac{1}{0.1} = 40 + 200 = 240 \text{ mg·hr/L}$$

\textbf{Clinical Significance:} The AUC represents total drug exposure over time and is crucial for determining bioequivalence and dosing regimens.
\end{solution}